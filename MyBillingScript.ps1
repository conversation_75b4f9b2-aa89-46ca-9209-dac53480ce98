# Define script version at the top
$scriptName = "Adam's Billing Manager"
$scriptVersion = "4.7.1"

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms.DataVisualization

# Create the main form
$form = New-Object System.Windows.Forms.Form
$form.Text = "$scriptName - V$scriptVersion"
$form.ClientSize = New-Object System.Drawing.Size(965, 800)  # Updated size to 965x750
$form.StartPosition = "CenterScreen"
$form.Icon = "PanAura.ico"
$form.MinimumSize = New-Object System.Drawing.Size(965, 800)  # Updated minimum size to match
$form.AutoSize = $false  # Enable manual resizing
$form.FormBorderStyle = [System.Windows.Forms.FormBorderStyle]::Sizable  # Ensure resizable
$form.MaximizeBox = $true  # Enable maximize button

# Add a label to display the current file name
$lblCurrentFile = New-Object System.Windows.Forms.Label
$lblCurrentFile.Text = "No file loaded"
$lblCurrentFile.Location = New-Object System.Drawing.Point(20, 20)
$lblCurrentFile.Size = New-Object System.Drawing.Size(880, 25)  # Adjusted to fit form width (940-60)
$lblCurrentFile.Font = New-Object System.Drawing.Font("Arial", 15, [System.Drawing.FontStyle]::Bold)
$lblCurrentFile.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$form.Controls.Add($lblCurrentFile)

# Create a ListView to display billing records
$listView = New-Object System.Windows.Forms.ListView
$listView.View = [System.Windows.Forms.View]::Details
$listView.FullRowSelect = $true
$listView.GridLines = $true
$listView.Location = New-Object System.Drawing.Point(20, 60)
$listView.Size = New-Object System.Drawing.Size(910, 486)  # Adjusted to fit form width (940-60)
$listView.Sorting = [System.Windows.Forms.SortOrder]::None
$listView.AllowColumnReorder = $true
$listView.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right -bor [System.Windows.Forms.AnchorStyles]::Bottom

# Add columns to the ListView with adjusted widths
$listView.Columns.Add("Bill Name", 165)  # Reduced to make room for category
$listView.Columns.Add("Amount", 75)
$listView.Columns.Add("Due Date", 85)
$listView.Columns.Add("Status", 120)
$listView.Columns.Add("Amount Paid", 75)
$listView.Columns.Add("Confirmation #", 110)
$listView.Columns.Add("Category", 100)  # New category column
$listView.Columns.Add("Notes", 165)  # Reduced to accommodate category

# Variables to track sorting
$script:lastColumnClicked = -1
$script:ascending = $true

# Define bill categories
$script:billCategories = @(
    "Housing",
    "Utilities",
    "Transportation",
    "Groceries",
    "Entertainment",
    "Healthcare/Medical",
    "Education",
    "Insurance",
    "Debt/Loans",
    "Subscriptions",
    "Personal Care",
    "Donations",
    "Miscellaneous"
)

# Function to handle column click for sorting
$listView.Add_ColumnClick({
    $columnIndex = $_.Column
    
    # If clicking the same column, toggle sort direction
    if ($columnIndex -eq $script:lastColumnClicked) {
        $script:ascending = !$script:ascending
    } else {
        # New column, default to ascending
        $script:ascending = $true
    }
    
    # Store the clicked column
    $script:lastColumnClicked = $columnIndex
    
    # Get all items from the ListView
    $items = New-Object System.Collections.ArrayList
    foreach ($item in $listView.Items) {
        # Ensure each item has enough SubItems
        while ($item.SubItems.Count -le $columnIndex) {
            $item.SubItems.Add("")
        }
        [void]$items.Add($item)
    }
    
    # Sort the items manually
    $listView.BeginUpdate()
    $listView.Items.Clear()
    
    # Sort based on column type
    if ($columnIndex -eq 1 -or $columnIndex -eq 4) {
        # Amount columns - numeric sorting
        if ($script:ascending) {
            $sortedItems = $items | Sort-Object -Property @{Expression={
                $value = $_.SubItems[$columnIndex].Text -replace '[$,]', ''
                if ([string]::IsNullOrEmpty($value)) { return [double]::MinValue }
                $num = $value -as [double]
                if ($null -eq $num) { return [double]::MinValue }
                return $num
            }}
        } else {
            $sortedItems = $items | Sort-Object -Property @{Expression={
                $value = $_.SubItems[$columnIndex].Text -replace '[$,]', ''
                if ([string]::IsNullOrEmpty($value)) { return [double]::MinValue }
                $num = $value -as [double]
                if ($null -eq $num) { return [double]::MinValue }
                return $num
            }} -Descending
        }
    } elseif ($columnIndex -eq 2) {
        # Due Date column - date sorting
        if ($script:ascending) {
            $sortedItems = $items | Sort-Object -Property @{Expression={
                $value = $_.SubItems[$columnIndex].Text
                if ([string]::IsNullOrEmpty($value)) { return [DateTime]::MinValue }
                try {
                    return [DateTime]::ParseExact($value, "MM/dd/yyyy", $null)
                } catch {
                    return [DateTime]::MinValue
                }
            }}
        } else {
            $sortedItems = $items | Sort-Object -Property @{Expression={
                $value = $_.SubItems[$columnIndex].Text
                if ([string]::IsNullOrEmpty($value)) { return [DateTime]::MinValue }
                try {
                    return [DateTime]::ParseExact($value, "MM/dd/yyyy", $null)
                } catch {
                    return [DateTime]::MinValue
                }
            }} -Descending
        }
    } else {
        # Default string comparison for other columns
        if ($script:ascending) {
            $sortedItems = $items | Sort-Object -Property @{Expression={$_.SubItems[$columnIndex].Text}}
        } else {
            $sortedItems = $items | Sort-Object -Property @{Expression={$_.SubItems[$columnIndex].Text}} -Descending
        }
    }
    
    # Add the sorted items back to the ListView
    foreach ($item in $sortedItems) {
        $listView.Items.Add($item)
    }
    
    $listView.EndUpdate()
    
    # Update colors after sorting
    foreach ($item in $listView.Items) {
        Update-ItemColors -item $item
    }
})


# Add controls for adding new bills - reorganized into a single line
$lblName = New-Object System.Windows.Forms.Label
$lblName.Text = "Bill Name:"
$lblName.Location = New-Object System.Drawing.Point(35, 555)
$lblName.Size = New-Object System.Drawing.Size(70, 20)
$lblName.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

$txtName = New-Object System.Windows.Forms.TextBox
$txtName.Location = New-Object System.Drawing.Point(105, 555)
$txtName.Size = New-Object System.Drawing.Size(140, 20)
$txtName.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

$lblAmount = New-Object System.Windows.Forms.Label
$lblAmount.Text = "Amount:"
$lblAmount.Location = New-Object System.Drawing.Point(255, 555)
$lblAmount.Size = New-Object System.Drawing.Size(50, 20)
$lblAmount.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

$txtAmount = New-Object System.Windows.Forms.TextBox
$txtAmount.Location = New-Object System.Drawing.Point(305, 555)
$txtAmount.Size = New-Object System.Drawing.Size(70, 20)
$txtAmount.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

# Validate amount input to ensure it's a valid decimal
$txtAmount.Add_TextChanged({
    if ($txtAmount.Text -and -not [decimal]::TryParse($txtAmount.Text.Replace('$','').Replace(',',''), [ref]$null)) {
        $txtAmount.BackColor = [System.Drawing.Color]::LightPink
    } else {
        $txtAmount.BackColor = [System.Drawing.SystemColors]::Window
    }
})

$lblDueDate = New-Object System.Windows.Forms.Label
$lblDueDate.Text = "Due Date:"
$lblDueDate.Location = New-Object System.Drawing.Point(385, 555)
$lblDueDate.Size = New-Object System.Drawing.Size(60, 20)
$lblDueDate.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

$dtpDueDate = New-Object System.Windows.Forms.DateTimePicker
$dtpDueDate.Location = New-Object System.Drawing.Point(445, 555)
$dtpDueDate.Size = New-Object System.Drawing.Size(100, 20)
$dtpDueDate.Format = [System.Windows.Forms.DateTimePickerFormat]::Short
$dtpDueDate.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

# Add category dropdown - moved to second row
$lblCategory = New-Object System.Windows.Forms.Label
$lblCategory.Text = "Category:"
$lblCategory.Location = New-Object System.Drawing.Point(35, 585)
$lblCategory.Size = New-Object System.Drawing.Size(60, 20)
$lblCategory.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

$cmbCategory = New-Object System.Windows.Forms.ComboBox
$cmbCategory.Location = New-Object System.Drawing.Point(105, 585)
$cmbCategory.Size = New-Object System.Drawing.Size(140, 20)
$cmbCategory.DropDownStyle = [System.Windows.Forms.ComboBoxStyle]::DropDownList
$cmbCategory.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left
foreach ($category in $script:billCategories) {
    $cmbCategory.Items.Add($category)
}
$cmbCategory.SelectedIndex = 0  # Default to first category

$lblConfirmation = New-Object System.Windows.Forms.Label
$lblConfirmation.Text = "Confirmation #:"
$lblConfirmation.Location = New-Object System.Drawing.Point(255, 585)
$lblConfirmation.Size = New-Object System.Drawing.Size(100, 20)
$lblConfirmation.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

$txtConfirmation = New-Object System.Windows.Forms.TextBox
$txtConfirmation.Location = New-Object System.Drawing.Point(355, 585)
$txtConfirmation.Size = New-Object System.Drawing.Size(110, 20)
$txtConfirmation.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

$btnAdd = New-Object System.Windows.Forms.Button
$btnAdd.Text = "Add Bill"
$btnAdd.Location = New-Object System.Drawing.Point(795, 552)
$btnAdd.Size = New-Object System.Drawing.Size(120, 30)
$btnAdd.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Right
# $btnAdd.BackColor = [System.Drawing.Color]::LightPink
$btnAdd.Font = New-Object System.Drawing.Font("Arial", 9, [System.Drawing.FontStyle]::Bold)



# Function to update item colors based on payment status, due date, and payment amount
function Update-ItemColors {
    param (
        [System.Windows.Forms.ListViewItem]$item
    )
    
    # Default to black text and regular font
    $item.ForeColor = [System.Drawing.Color]::Black
    $item.Font = New-Object System.Drawing.Font($listView.Font, [System.Drawing.FontStyle]::Regular)
    
    # If paid, check payment amount vs bill amount
    if ($item.SubItems[3].Text.StartsWith("Paid")) {
        # Default paid color is green
        $item.ForeColor = [System.Drawing.Color]::Green
        
        # Check if we have amount and amount paid to compare
        if ($item.SubItems.Count -gt 4 -and -not [string]::IsNullOrWhiteSpace($item.SubItems[4].Text)) {
            try {
                # Parse the original amount and amount paid
                $originalAmountText = $item.SubItems[1].Text -replace '[$,]', ''
                $amountPaidText = $item.SubItems[4].Text -replace '[$,]', ''
                
                if ([decimal]::TryParse($originalAmountText, [ref]$null) -and [decimal]::TryParse($amountPaidText, [ref]$null)) {
                    $originalAmount = [decimal]::Parse($originalAmountText)
                    $amountPaid = [decimal]::Parse($amountPaidText)
                    
                    # Compare amounts
                    if ($amountPaid -lt $originalAmount) {
                        # Underpaid - set to orange and bold
                        $item.ForeColor = [System.Drawing.Color]::Orange
                        $item.Font = New-Object System.Drawing.Font($listView.Font, [System.Drawing.FontStyle]::Bold)
                    } elseif ($amountPaid -gt $originalAmount) {
                        # Overpaid - set to blue
                        $item.ForeColor = [System.Drawing.Color]::Blue
                    }
                    # If equal, keep the default green
                }
            } catch {
                # If parsing fails, keep as green
            }
        }
        return
    }
    
    # Check if past due
    try {
        $dueDate = [DateTime]::ParseExact($item.SubItems[2].Text, "MM/dd/yyyy", $null)
        $today = Get-Date
        
        # If unpaid and past due, set to red
        if ($dueDate -lt $today) {
            $item.ForeColor = [System.Drawing.Color]::Red
        }
    } catch {
        # If date parsing fails, leave as black
    }
}

# Function to add a new bill
$btnAdd.Add_Click({
    if ($txtName.Text -and $txtAmount.Text) {
        # Validate amount is a valid decimal
        if (-not [decimal]::TryParse($txtAmount.Text.Replace('$','').Replace(',',''), [ref]$null)) {
            [System.Windows.Forms.MessageBox]::Show(
                "Please enter a valid amount (numbers only).", 
                "Invalid Amount", 
                [System.Windows.Forms.MessageBoxButtons]::OK, 
                [System.Windows.Forms.MessageBoxIcon]::Warning)
            $txtAmount.Focus()
            return
        }
        
        $item = New-Object System.Windows.Forms.ListViewItem($txtName.Text)
        $item.SubItems.Add($txtAmount.Text)
        $item.SubItems.Add($dtpDueDate.Value.ToString("MM/dd/yyyy"))
        $item.SubItems.Add("Unpaid")
        $item.SubItems.Add("")  # Empty amount paid
        $item.SubItems.Add($txtConfirmation.Text) # Add confirmation number
        $item.SubItems.Add($cmbCategory.SelectedItem.ToString()) # Add category
        $item.SubItems.Add("") # Empty notes
        
        # Set color based on due date
        Update-ItemColors -item $item
        
        $listView.Items.Add($item)
        
        # Clear input fields
        $txtName.Text = ""
        $txtAmount.Text = ""
        $txtConfirmation.Text = ""
        $cmbCategory.SelectedIndex = 0  # Reset to first category

        # Focus back on the Bill Name field
        $txtName.Focus()
    }
})

# Add controls to the form
$form.Controls.Add($listView)
$form.Controls.Add($lblName)
$form.Controls.Add($txtName)
$form.Controls.Add($lblAmount)
$form.Controls.Add($txtAmount)
$form.Controls.Add($lblDueDate)
$form.Controls.Add($dtpDueDate)
$form.Controls.Add($lblCategory)
$form.Controls.Add($cmbCategory)
$form.Controls.Add($lblConfirmation)
$form.Controls.Add($txtConfirmation)
$form.Controls.Add($btnAdd)

# Add a button to mark bills as paid
$btnMarkPaid = New-Object System.Windows.Forms.Button
$btnMarkPaid.Text = "Mark as Paid"
$btnMarkPaid.Location = New-Object System.Drawing.Point(740, 630)  # Adjusted for 940px width
$btnMarkPaid.Size = New-Object System.Drawing.Size(165, 50)
$btnMarkPaid.BackColor = [System.Drawing.Color]::LightGreen
$btnMarkPaid.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
$btnMarkPaid.Font = New-Object System.Drawing.Font("Arial", 9, [System.Drawing.FontStyle]::Bold)
$btnMarkPaid.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Right

# Function to toggle paid status of selected bills
$btnMarkPaid.Add_Click({
    $selectedItems = $listView.SelectedItems
    if ($selectedItems.Count -gt 0) {
        # Check if any of the selected items are unpaid
        $hasUnpaidItems = $false
        foreach ($item in $selectedItems) {
            if ($item.SubItems[3].Text -eq "Unpaid") {
                $hasUnpaidItems = $true
                break
            }
        }

        # If marking as paid, ask for confirmation number and amount paid
        if ($hasUnpaidItems) {
            # Create a form for payment details input
            $confirmForm = New-Object System.Windows.Forms.Form
            $confirmForm.Text = "Payment Details"
            $confirmForm.Size = New-Object System.Drawing.Size(400, 280)  # Increased height for payment date
            $confirmForm.StartPosition = "CenterParent"
            $confirmForm.FormBorderStyle = "FixedDialog"
            $confirmForm.MaximizeBox = $false
            $confirmForm.MinimizeBox = $false

            # Add label for payment date
            $lblPaymentDate = New-Object System.Windows.Forms.Label
            $lblPaymentDate.Text = "Payment Date:"
            $lblPaymentDate.Location = New-Object System.Drawing.Point(20, 20)
            $lblPaymentDate.Size = New-Object System.Drawing.Size(120, 20)
            $confirmForm.Controls.Add($lblPaymentDate)

            # Add DateTimePicker for payment date
            $dtpPaymentDate = New-Object System.Windows.Forms.DateTimePicker
            $dtpPaymentDate.Location = New-Object System.Drawing.Point(150, 20)
            $dtpPaymentDate.Size = New-Object System.Drawing.Size(220, 25)
            $dtpPaymentDate.Format = [System.Windows.Forms.DateTimePickerFormat]::Short
            $dtpPaymentDate.Value = Get-Date  # Default to current date
            $confirmForm.Controls.Add($dtpPaymentDate)

            # Add label for amount paid (moved down)
            $lblAmountPaid = New-Object System.Windows.Forms.Label
            $lblAmountPaid.Text = "Amount Paid:"
            $lblAmountPaid.Location = New-Object System.Drawing.Point(20, 60)
            $lblAmountPaid.Size = New-Object System.Drawing.Size(120, 20)
            $confirmForm.Controls.Add($lblAmountPaid)

            # Add textbox for amount paid (moved down)
            $txtAmountPaid = New-Object System.Windows.Forms.TextBox
            $txtAmountPaid.Location = New-Object System.Drawing.Point(150, 60)
            $txtAmountPaid.Size = New-Object System.Drawing.Size(220, 25)
            # Default to the bill amount for the first selected item
            if ($selectedItems.Count -gt 0) {
                $txtAmountPaid.Text = $selectedItems[0].SubItems[1].Text
            }
            $confirmForm.Controls.Add($txtAmountPaid)

            # Add label for confirmation number (moved down)
            $lblConfirmPrompt = New-Object System.Windows.Forms.Label
            $lblConfirmPrompt.Text = "Confirmation Number (optional):"
            $lblConfirmPrompt.Location = New-Object System.Drawing.Point(20, 100)
            $lblConfirmPrompt.Size = New-Object System.Drawing.Size(200, 20)
            $confirmForm.Controls.Add($lblConfirmPrompt)

            # Add textbox for confirmation number (moved down)
            $txtConfirmPrompt = New-Object System.Windows.Forms.TextBox
            $txtConfirmPrompt.Location = New-Object System.Drawing.Point(20, 130)
            $txtConfirmPrompt.Size = New-Object System.Drawing.Size(350, 25)
            $confirmForm.Controls.Add($txtConfirmPrompt)

            # Add OK button (moved down)
            $btnConfirmOK = New-Object System.Windows.Forms.Button
            $btnConfirmOK.Text = "OK"
            $btnConfirmOK.DialogResult = [System.Windows.Forms.DialogResult]::OK
            $btnConfirmOK.Location = New-Object System.Drawing.Point(110, 190)
            $btnConfirmOK.Size = New-Object System.Drawing.Size(80, 30)
            $confirmForm.Controls.Add($btnConfirmOK)

            # Add Cancel button (moved down)
            $btnConfirmCancel = New-Object System.Windows.Forms.Button
            $btnConfirmCancel.Text = "Cancel"
            $btnConfirmCancel.DialogResult = [System.Windows.Forms.DialogResult]::Cancel
            $btnConfirmCancel.Location = New-Object System.Drawing.Point(200, 190)
            $btnConfirmCancel.Size = New-Object System.Drawing.Size(80, 30)
            $confirmForm.Controls.Add($btnConfirmCancel)

            $confirmForm.AcceptButton = $btnConfirmOK
            $confirmForm.CancelButton = $btnConfirmCancel

            # Show the form and get result
            $result = $confirmForm.ShowDialog()

            if ($result -eq [System.Windows.Forms.DialogResult]::OK) {
                $amountPaid = $txtAmountPaid.Text
                $confirmationNumber = $txtConfirmPrompt.Text

                # Process the selected items
                foreach ($item in $selectedItems) {
                    if ($item.SubItems[3].Text -eq "Unpaid") {
                        # Use the selected payment date instead of current date
                        $paymentDate = $dtpPaymentDate.Value.ToString("MM/dd/yyyy")
                        $item.SubItems[3].Text = "Paid on $paymentDate"

                        # Set amount paid
                        # Make sure we have enough SubItems
                        while ($item.SubItems.Count -lt 7) {
                            $item.SubItems.Add("")
                        }
                        $item.SubItems[4].Text = $amountPaid

                        # Set confirmation number if provided
                        if ($confirmationNumber) {
                            $item.SubItems[5].Text = $confirmationNumber
                        }

                        # Update color based on payment status and amount
                        Update-ItemColors -item $item
                    } else {
                        $item.SubItems[3].Text = "Unpaid"
                        # Update color based on due date
                        Update-ItemColors -item $item
                        $item.SubItems[4].Text = ""  # Clear amount paid
                    }
                }
            }
        } else {
            # Just toggle back to unpaid without asking for confirmation
            foreach ($item in $selectedItems) {
                $item.SubItems[3].Text = "Unpaid"
                # Update color based on due date
                Update-ItemColors -item $item
                $item.SubItems[4].Text = ""  # Clear amount paid
            }
        }
    } else {
        [System.Windows.Forms.MessageBox]::Show("Please select at least one bill to mark as paid.", "No Selection", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    }
})

# Add the button to the form
$form.Controls.Add($btnMarkPaid)

# Add Save and Load buttons to the form
$btnSave = New-Object System.Windows.Forms.Button
$btnSave.Text = "Save Bills"
$btnSave.Location = New-Object System.Drawing.Point(60, 640)
$btnSave.Size = New-Object System.Drawing.Size(120, 30)
$btnSave.BackColor = [System.Drawing.Color]::Thistle
$btnSave.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

$btnLoad = New-Object System.Windows.Forms.Button
$btnLoad.Text = "Load Bills"
$btnLoad.Location = New-Object System.Drawing.Point(190, 640)
$btnLoad.Size = New-Object System.Drawing.Size(120, 30)
$btnLoad.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

# Function to save bills to a file
$btnSave.Add_Click({
    $saveFileDialog = New-Object System.Windows.Forms.SaveFileDialog
    $saveFileDialog.Filter = "XML files (*.xml)|*.xml|All files (*.*)|*.*"
    $saveFileDialog.DefaultExt = "xml"
    $saveFileDialog.AddExtension = $true
    
    if ($saveFileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        $bills = @()
        
        foreach ($item in $listView.Items) {
            # Ensure we have all SubItems (now 8 total: name + 7 subitems)
            while ($item.SubItems.Count -lt 8) {
                $item.SubItems.Add("")
            }

            # Check if the bill is paid and extract payment date if available
            $isPaid = $item.SubItems[3].Text.StartsWith("Paid")
            $paymentDate = ""
            if ($isPaid -and $item.SubItems[3].Text -match "Paid on (\d{2}/\d{2}/\d{4})") {
                $paymentDate = $matches[1]
            }

            $bill = @{
                Name = $item.Text
                Amount = $item.SubItems[1].Text
                DueDate = $item.SubItems[2].Text
                Status = $item.SubItems[3].Text
                AmountPaid = $item.SubItems[4].Text
                ConfirmationNumber = $item.SubItems[5].Text
                Category = $item.SubItems[6].Text
                Notes = $item.SubItems[7].Text
                PaymentDate = $paymentDate
                IsPaid = $isPaid
            }

            # Add to bills array
            $bills += $bill
        }
        
        # Convert the array to XML and save it
        $bills | Export-Clixml -Path $saveFileDialog.FileName
        
        # Update the current file label
        $fileName = [System.IO.Path]::GetFileName($saveFileDialog.FileName)
        $lblCurrentFile.Text = "Current File: $fileName"
        
        [System.Windows.Forms.MessageBox]::Show("Bills saved successfully!", "Save Complete", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    }
})

# Function to load bills from a file
$btnLoad.Add_Click({
    $openFileDialog = New-Object System.Windows.Forms.OpenFileDialog
    $openFileDialog.Filter = "XML files (*.xml)|*.xml|All files (*.*)|*.*"
    
    if ($openFileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        try {
            # Clear existing items
            $listView.Items.Clear()
            
            # Import the XML file
            $bills = Import-Clixml -Path $openFileDialog.FileName
            
            # Update the current file label
            $fileName = [System.IO.Path]::GetFileName($openFileDialog.FileName)
            $lblCurrentFile.Text = "Current File:" + "      " + "$fileName"
            
            foreach ($bill in $bills) {
                # Create new item with the bill name
                $item = New-Object System.Windows.Forms.ListViewItem($bill.Name)
                
                # Add amount and due date
                $item.SubItems.Add($bill.Amount)
                $item.SubItems.Add($bill.DueDate)
                $item.SubItems.Add($bill.Status)
                
                # Add amount paid if it exists
                if ($null -ne $bill.AmountPaid) {
                    $item.SubItems.Add($bill.AmountPaid)
                } else {
                    $item.SubItems.Add("")
                }
                
                # Add confirmation number if it exists
                if ($null -ne $bill.ConfirmationNumber) {
                    $item.SubItems.Add($bill.ConfirmationNumber)
                } else {
                    $item.SubItems.Add("")
                }

                # Add category if it exists (for backward compatibility with old files)
                if ($null -ne $bill.Category) {
                    $item.SubItems.Add($bill.Category)
                } else {
                    $item.SubItems.Add("Miscellaneous")  # Default category for old files
                }

                # Add notes if they exist
                if ($null -ne $bill.Notes) {
                    $item.SubItems.Add($bill.Notes)
                } else {
                    $item.SubItems.Add("")
                }
                
                # Update color based on payment status and due date
                Update-ItemColors -item $item
                
                # Add the item to the list view
                $listView.Items.Add($item)
            }
            
            [System.Windows.Forms.MessageBox]::Show("Bills loaded successfully!", "Load Complete", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
            
            # Check for upcoming bills after successful load
            Show-UpcomingBillsReminder
        }
        catch {
            [System.Windows.Forms.MessageBox]::Show("Error loading bills: $_", "Load Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
        }
    }
})

# Add the buttons to the form
$form.Controls.Add($btnSave)
$form.Controls.Add($btnLoad)

# Add a Delete button to the form
$btnDelete = New-Object System.Windows.Forms.Button
$btnDelete.Text = "Delete Bill"
$btnDelete.Location = New-Object System.Drawing.Point(320, 640)
$btnDelete.Size = New-Object System.Drawing.Size(120, 30)
$btnDelete.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

# Function to delete selected bills
$btnDelete.Add_Click({
    $selectedItems = $listView.SelectedItems
    if ($selectedItems.Count -gt 0) {
        $result = [System.Windows.Forms.MessageBox]::Show(
            "Are you sure you want to delete the selected bill(s)?", 
            "Confirm Delete", 
            [System.Windows.Forms.MessageBoxButtons]::YesNo, 
            [System.Windows.Forms.MessageBoxIcon]::Question)
            
        if ($result -eq [System.Windows.Forms.DialogResult]::Yes) {
            # We need to remove items from the end to avoid index changes affecting the removal
            for ($i = $selectedItems.Count - 1; $i -ge 0; $i--) {
                $listView.Items.Remove($selectedItems[$i])
            }
        }
    } else {
        [System.Windows.Forms.MessageBox]::Show(
            "Please select at least one bill to delete.", 
            "No Selection", 
            [System.Windows.Forms.MessageBoxButtons]::OK, 
            [System.Windows.Forms.MessageBoxIcon]::Information)
    }
})

# Add the Delete button to the form
$form.Controls.Add($btnDelete)


# Create the label control for the line divider (now used as box for buttons behind save, load and delete buttons)
$line = New-Object System.Windows.Forms.Label
$line.AutoSize = $false
$line.BorderStyle = 'Fixed3D'
$line.Text = ""
$line.Height = 50
$line.Width = 420  # Adjust width as needed
$line.Location = New-Object System.Drawing.Point(40, 630) #Adjust Location as needed

# Add the label control to the line to theform
$form.Controls.Add($line)




# Add an Edit button to the form
$btnEdit = New-Object System.Windows.Forms.Button
$btnEdit.Text = "Edit Bill"
$btnEdit.Location = New-Object System.Drawing.Point(490, 630)
$btnEdit.Size = New-Object System.Drawing.Size(220, 50)
$btnEdit.BackColor = [System.Drawing.Color]::LightYellow
$btnEdit.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
$btnEdit.Font = New-Object System.Drawing.Font("Arial", 9, [System.Drawing.FontStyle]::Bold)
$btnEdit.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left


# Function to edit selected bill
$btnEdit.Add_Click({
    $selectedItems = $listView.SelectedItems
    if ($selectedItems.Count -eq 1) {
        # Get the selected item
        $selectedItem = $selectedItems[0]
        
        # Create a new form for editing
        $editForm = New-Object System.Windows.Forms.Form
        $editForm.Text = "Edit Bill"
        $editForm.Size = New-Object System.Drawing.Size(400, 500)  # Increased height for category control
        $editForm.StartPosition = "CenterParent"
        $editForm.FormBorderStyle = "FixedDialog"
        $editForm.MaximizeBox = $false
        $editForm.MinimizeBox = $false
        
        # Add controls for editing (name, amount, due date)
        $lblEditName = New-Object System.Windows.Forms.Label
        $lblEditName.Text = "Bill Name:"
        $lblEditName.Location = New-Object System.Drawing.Point(20, 20)
        $lblEditName.Size = New-Object System.Drawing.Size(80, 20)
        $editForm.Controls.Add($lblEditName)
        
        $txtEditName = New-Object System.Windows.Forms.TextBox
        $txtEditName.Text = $selectedItem.Text
        $txtEditName.Location = New-Object System.Drawing.Point(120, 20)
        $txtEditName.Size = New-Object System.Drawing.Size(240, 20)
        $editForm.Controls.Add($txtEditName)
        
        $lblEditAmount = New-Object System.Windows.Forms.Label
        $lblEditAmount.Text = "Amount:"
        $lblEditAmount.Location = New-Object System.Drawing.Point(20, 60)
        $lblEditAmount.Size = New-Object System.Drawing.Size(80, 20)
        $editForm.Controls.Add($lblEditAmount)
        
        $txtEditAmount = New-Object System.Windows.Forms.TextBox
        $txtEditAmount.Text = $selectedItem.SubItems[1].Text
        $txtEditAmount.Location = New-Object System.Drawing.Point(120, 60)
        $txtEditAmount.Size = New-Object System.Drawing.Size(240, 20)
        $editForm.Controls.Add($txtEditAmount)
        
        $lblEditDueDate = New-Object System.Windows.Forms.Label
        $lblEditDueDate.Text = "Due Date:"
        $lblEditDueDate.Location = New-Object System.Drawing.Point(20, 100)
        $lblEditDueDate.Size = New-Object System.Drawing.Size(80, 20)
        $editForm.Controls.Add($lblEditDueDate)
        
        $dtpEditDueDate = New-Object System.Windows.Forms.DateTimePicker
        try {
            $dtpEditDueDate.Value = [DateTime]::ParseExact($selectedItem.SubItems[2].Text, "MM/dd/yyyy", $null)
        } catch {
            $dtpEditDueDate.Value = Get-Date
        }
        $dtpEditDueDate.Location = New-Object System.Drawing.Point(120, 100)
        $dtpEditDueDate.Size = New-Object System.Drawing.Size(240, 20)
        $dtpEditDueDate.Format = [System.Windows.Forms.DateTimePickerFormat]::Short
        $editForm.Controls.Add($dtpEditDueDate)
        
        # Add Payment Status and Date controls
        $lblEditStatus = New-Object System.Windows.Forms.Label
        $lblEditStatus.Text = "Payment Status:"
        $lblEditStatus.Location = New-Object System.Drawing.Point(20, 140)
        $lblEditStatus.Size = New-Object System.Drawing.Size(100, 20)
        $editForm.Controls.Add($lblEditStatus)
        
        $cbEditPaid = New-Object System.Windows.Forms.CheckBox
        $cbEditPaid.Text = "Paid"
        $cbEditPaid.Location = New-Object System.Drawing.Point(120, 140)
        $cbEditPaid.Size = New-Object System.Drawing.Size(60, 20)
        $cbEditPaid.Checked = $selectedItem.SubItems[3].Text.StartsWith("Paid")
        $editForm.Controls.Add($cbEditPaid)
        
        $lblEditPaymentDate = New-Object System.Windows.Forms.Label
        $lblEditPaymentDate.Text = "Payment Date:"
        $lblEditPaymentDate.Location = New-Object System.Drawing.Point(20, 180)
        $lblEditPaymentDate.Size = New-Object System.Drawing.Size(100, 20)
        $editForm.Controls.Add($lblEditPaymentDate)
        
        $dtpEditPaymentDate = New-Object System.Windows.Forms.DateTimePicker
        $dtpEditPaymentDate.Location = New-Object System.Drawing.Point(120, 180)
        $dtpEditPaymentDate.Size = New-Object System.Drawing.Size(240, 20)
        $dtpEditPaymentDate.Format = [System.Windows.Forms.DateTimePickerFormat]::Short
        $dtpEditPaymentDate.Enabled = $cbEditPaid.Checked
        
        # Try to extract payment date from status
        if ($selectedItem.SubItems[3].Text -match "Paid on (\d{2}/\d{2}/\d{4})") {
            try {
                $dtpEditPaymentDate.Value = [DateTime]::ParseExact($matches[1], "MM/dd/yyyy", $null)
            } catch {
                $dtpEditPaymentDate.Value = Get-Date
            }
        } else {
            $dtpEditPaymentDate.Value = Get-Date
        }
        $editForm.Controls.Add($dtpEditPaymentDate)
        
        # Add Amount Paid field
        $lblEditAmountPaid = New-Object System.Windows.Forms.Label
        $lblEditAmountPaid.Text = "Amount Paid:"
        $lblEditAmountPaid.Location = New-Object System.Drawing.Point(20, 220)
        $lblEditAmountPaid.Size = New-Object System.Drawing.Size(100, 20)
        $editForm.Controls.Add($lblEditAmountPaid)
        
        $txtEditAmountPaid = New-Object System.Windows.Forms.TextBox
        # Make sure we're accessing the correct SubItems index for Amount Paid (index 4)
        if ($selectedItem.SubItems.Count -gt 4) {
            $txtEditAmountPaid.Text = $selectedItem.SubItems[4].Text
        } else {
            $txtEditAmountPaid.Text = ""
        }
        $txtEditAmountPaid.Location = New-Object System.Drawing.Point(120, 220)
        $txtEditAmountPaid.Size = New-Object System.Drawing.Size(240, 20)
        $txtEditAmountPaid.Enabled = $cbEditPaid.Checked
        $editForm.Controls.Add($txtEditAmountPaid)
        
        # Enable/disable payment date and amount paid based on paid status
        $cbEditPaid.Add_CheckedChanged({
            $dtpEditPaymentDate.Enabled = $cbEditPaid.Checked
            $txtEditAmountPaid.Enabled = $cbEditPaid.Checked
            
            # If checked and amount paid is empty, default to bill amount
            if ($cbEditPaid.Checked -and [string]::IsNullOrEmpty($txtEditAmountPaid.Text)) {
                $txtEditAmountPaid.Text = $txtEditAmount.Text
            }
        })
        
        # Add Confirmation Number field
        $lblEditConfirmation = New-Object System.Windows.Forms.Label
        $lblEditConfirmation.Text = "Confirmation #:"
        $lblEditConfirmation.Location = New-Object System.Drawing.Point(20, 260)
        $lblEditConfirmation.Size = New-Object System.Drawing.Size(100, 20)
        $editForm.Controls.Add($lblEditConfirmation)
        
        $txtEditConfirmation = New-Object System.Windows.Forms.TextBox
        # Make sure we're accessing the correct SubItems index for Confirmation (index 5)
        if ($selectedItem.SubItems.Count -gt 5) {
            $txtEditConfirmation.Text = $selectedItem.SubItems[5].Text
        } else {
            $txtEditConfirmation.Text = ""
        }
        $txtEditConfirmation.Location = New-Object System.Drawing.Point(120, 260)
        $txtEditConfirmation.Size = New-Object System.Drawing.Size(240, 20)
        $editForm.Controls.Add($txtEditConfirmation)

        # Add Category field
        $lblEditCategory = New-Object System.Windows.Forms.Label
        $lblEditCategory.Text = "Category:"
        $lblEditCategory.Location = New-Object System.Drawing.Point(20, 300)
        $lblEditCategory.Size = New-Object System.Drawing.Size(80, 20)
        $editForm.Controls.Add($lblEditCategory)

        $cmbEditCategory = New-Object System.Windows.Forms.ComboBox
        $cmbEditCategory.Location = New-Object System.Drawing.Point(120, 300)
        $cmbEditCategory.Size = New-Object System.Drawing.Size(240, 20)
        $cmbEditCategory.DropDownStyle = [System.Windows.Forms.ComboBoxStyle]::DropDownList
        foreach ($category in $script:billCategories) {
            $cmbEditCategory.Items.Add($category)
        }
        # Set current category (index 6)
        if ($selectedItem.SubItems.Count -gt 6 -and $selectedItem.SubItems[6].Text) {
            $cmbEditCategory.SelectedItem = $selectedItem.SubItems[6].Text
        } else {
            $cmbEditCategory.SelectedIndex = 0  # Default to first category
        }
        $editForm.Controls.Add($cmbEditCategory)

        # Add Notes field
        $lblEditNotes = New-Object System.Windows.Forms.Label
        $lblEditNotes.Text = "Notes:"
        $lblEditNotes.Location = New-Object System.Drawing.Point(20, 340)
        $lblEditNotes.Size = New-Object System.Drawing.Size(80, 20)
        $editForm.Controls.Add($lblEditNotes)

        $txtEditNotes = New-Object System.Windows.Forms.TextBox
        # Make sure we're accessing the correct SubItems index for Notes (now index 7)
        if ($selectedItem.SubItems.Count -gt 7) {
            $txtEditNotes.Text = $selectedItem.SubItems[7].Text
        } else {
            $txtEditNotes.Text = ""
        }
        $txtEditNotes.Location = New-Object System.Drawing.Point(120, 340)
        $txtEditNotes.Size = New-Object System.Drawing.Size(240, 60)
        $txtEditNotes.Multiline = $true
        $editForm.Controls.Add($txtEditNotes)
        
        # Add Save and Cancel buttons
        $btnSaveEdit = New-Object System.Windows.Forms.Button
        $btnSaveEdit.Text = "Save"
        $btnSaveEdit.DialogResult = [System.Windows.Forms.DialogResult]::OK
        $btnSaveEdit.Location = New-Object System.Drawing.Point(120, 410)
        $btnSaveEdit.Size = New-Object System.Drawing.Size(100, 30)
        $editForm.Controls.Add($btnSaveEdit)

        $btnCancelEdit = New-Object System.Windows.Forms.Button
        $btnCancelEdit.Text = "Cancel"
        $btnCancelEdit.DialogResult = [System.Windows.Forms.DialogResult]::Cancel
        $btnCancelEdit.Location = New-Object System.Drawing.Point(240, 410)
        $btnCancelEdit.Size = New-Object System.Drawing.Size(100, 30)
        $editForm.Controls.Add($btnCancelEdit)
        
        $editForm.AcceptButton = $btnSaveEdit
        $editForm.CancelButton = $btnCancelEdit
        
        # Show the form and update if OK is clicked
        $result = $editForm.ShowDialog()
        if ($result -eq [System.Windows.Forms.DialogResult]::OK) {
            $selectedItem.Text = $txtEditName.Text
            $selectedItem.SubItems[1].Text = $txtEditAmount.Text
            $selectedItem.SubItems[2].Text = $dtpEditDueDate.Value.ToString("MM/dd/yyyy")
            
            # Update paid status with payment date
            if ($cbEditPaid.Checked) {
                $paymentDate = $dtpEditPaymentDate.Value.ToString("MM/dd/yyyy")
                $selectedItem.SubItems[3].Text = "Paid on $paymentDate"
                
                # Make sure we have enough SubItems before trying to set them
                while ($selectedItem.SubItems.Count -lt 8) {
                    $selectedItem.SubItems.Add("")
                }

                # Update amount paid
                $selectedItem.SubItems[4].Text = $txtEditAmountPaid.Text

                # Update color based on payment status and amount
                Update-ItemColors -item $selectedItem
            } else {
                $selectedItem.SubItems[3].Text = "Unpaid"
                # Update color based on due date
                Update-ItemColors -item $selectedItem
            }

            # Make sure we have enough SubItems before trying to set them
            while ($selectedItem.SubItems.Count -lt 8) {
                $selectedItem.SubItems.Add("")
            }

            $selectedItem.SubItems[5].Text = $txtEditConfirmation.Text
            $selectedItem.SubItems[6].Text = $cmbEditCategory.SelectedItem.ToString()
            $selectedItem.SubItems[7].Text = $txtEditNotes.Text
        }
    } elseif ($selectedItems.Count -gt 1) {
        [System.Windows.Forms.MessageBox]::Show("Please select only one bill to edit.", "Multiple Selection", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    } else {
        [System.Windows.Forms.MessageBox]::Show("Please select a bill to edit.", "No Selection", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    }
})

# Add the Edit button to the form
$form.Controls.Add($btnEdit)

# Add an Export to CSV option to the Save button's context menu
$btnExportCSV = New-Object System.Windows.Forms.Button
$btnExportCSV.Text = "Export to CSV"
$btnExportCSV.Location = New-Object System.Drawing.Point(20, 650)
$btnExportCSV.Size = New-Object System.Drawing.Size(120, 30)
$btnExportCSV.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

# Function to export bills to a CSV file
$btnExportCSV.Add_Click({
    $saveFileDialog = New-Object System.Windows.Forms.SaveFileDialog
    $saveFileDialog.Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*"
    $saveFileDialog.DefaultExt = "csv"
    $saveFileDialog.AddExtension = $true
    
    if ($saveFileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        try {
            # Create CSV content
            $csvContent = "Bill Name,Amount,Due Date,Status,Amount Paid,Confirmation Number,Category,Notes`r`n"

            foreach ($item in $listView.Items) {
                # Handle commas in fields by enclosing in quotes
                $billName = """$($item.Text -replace '"', '""')"""
                $amount = """$($item.SubItems[1].Text -replace '"', '""')"""
                $dueDate = """$($item.SubItems[2].Text -replace '"', '""')"""
                $status = """$($item.SubItems[3].Text -replace '"', '""')"""
                $amountPaid = """$($item.SubItems[4].Text -replace '"', '""')"""
                # Add a leading apostrophe to ensure Excel treats it as text
                $confirmationNumber = """'$($item.SubItems[5].Text -replace '"', '""')"""
                $category = """$($item.SubItems[6].Text -replace '"', '""')"""
                $notes = """$($item.SubItems[7].Text -replace '"', '""')"""

                $csvContent += "$billName,$amount,$dueDate,$status,$amountPaid,$confirmationNumber,$category,$notes`r`n"
            }
            
            # Write to file
            [System.IO.File]::WriteAllText($saveFileDialog.FileName, $csvContent)
            
            [System.Windows.Forms.MessageBox]::Show(
                "Bills exported to CSV successfully!", 
                "Export Complete", 
                [System.Windows.Forms.MessageBoxButtons]::OK, 
                [System.Windows.Forms.MessageBoxIcon]::Information)
        }
        catch {
            [System.Windows.Forms.MessageBox]::Show(
                "Error exporting to CSV: $_", 
                "Export Error", 
                [System.Windows.Forms.MessageBoxButtons]::OK, 
                [System.Windows.Forms.MessageBoxIcon]::Error)
        }
    }
})

# Add the Export button to the form
$form.Controls.Add($btnExportCSV)


# Add a Monthly Summary button
$btnMonthlySummary = New-Object System.Windows.Forms.Button
$btnMonthlySummary.Text = "Monthly Summary"
$btnMonthlySummary.Location = New-Object System.Drawing.Point(150, 650)
$btnMonthlySummary.Size = New-Object System.Drawing.Size(120, 30)
$btnMonthlySummary.Anchor = [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left

# Function to show monthly summary
$btnMonthlySummary.Add_Click({
    # Initialize counters
    $totalDue = 0
    $totalPaid = 0
    $totalAmountPaid = 0
    $unpaidBills = @()
    $paidBills = @()
    $categoryTotals = @{}

    # Initialize category totals
    foreach ($category in $script:billCategories) {
        $categoryTotals[$category] = 0
    }

    # Loop through all bills
    foreach ($item in $listView.Items) {
        try {
            # Parse the amount (remove currency symbols and commas)
            $amountText = $item.SubItems[1].Text -replace '[$,]', ''
            $amount = [decimal]::Parse($amountText)

            # Get category (index 6, default to Miscellaneous if empty)
            $category = if ($item.SubItems.Count -gt 6 -and $item.SubItems[6].Text) {
                $item.SubItems[6].Text
            } else {
                "Miscellaneous"
            }

            # Add to category total
            if ($categoryTotals.ContainsKey($category)) {
                $categoryTotals[$category] += $amount
            } else {
                $categoryTotals[$category] = $amount
            }

            # Check if the bill is paid or unpaid
            if ($item.SubItems[3].Text.StartsWith("Paid")) {
                $totalPaid += $amount
                $paidBills += "$($item.Text) - $($item.SubItems[1].Text) - $($item.SubItems[3].Text) - [$category]"

                # Calculate actual amount paid (if available)
                if (-not [string]::IsNullOrWhiteSpace($item.SubItems[4].Text)) {
                    $amountPaidText = $item.SubItems[4].Text -replace '[$,]', ''
                    if ([decimal]::TryParse($amountPaidText, [ref]$null)) {
                        $amountPaid = [decimal]::Parse($amountPaidText)
                        $totalAmountPaid += $amountPaid
                    }
                }
            } else {
                $totalDue += $amount
                $unpaidBills += "$($item.Text) - $($item.SubItems[1].Text) - Due: $($item.SubItems[2].Text) - [$category]"
            }
        } catch {
            # Skip bills with invalid amounts
            continue
        }
    }
    
    # Calculate overpayment
    $overpayment = $totalAmountPaid - $totalPaid
    
    # Create a summary form with better layout for pie chart
    $summaryForm = New-Object System.Windows.Forms.Form
    $summaryForm.Text = "Bills Summary with Category Breakdown"
    $summaryForm.Size = New-Object System.Drawing.Size(1020, 710) # Increased height for much taller bill lists
    $summaryForm.StartPosition = "CenterParent"
    $summaryForm.FormBorderStyle = "FixedDialog"
    $summaryForm.MaximizeBox = $false
    $summaryForm.MinimizeBox = $false
    
    # Add a label for the title
    $lblTitle = New-Object System.Windows.Forms.Label
    $lblTitle.Text = "Complete Bills Summary"
    $lblTitle.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
    $lblTitle.Location = New-Object System.Drawing.Point(20, 20)
    $lblTitle.Size = New-Object System.Drawing.Size(460, 25)
    $summaryForm.Controls.Add($lblTitle)
    
    # Add a label for total due
    $lblTotalDue = New-Object System.Windows.Forms.Label
    $lblTotalDue.Text = "Total Amount Due: $($totalDue.ToString('C'))"
    $lblTotalDue.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Regular)
    $lblTotalDue.Location = New-Object System.Drawing.Point(20, 60)
    $lblTotalDue.Size = New-Object System.Drawing.Size(460, 20)
    $summaryForm.Controls.Add($lblTotalDue)
    
    # Add a label for total paid
    $lblTotalPaid = New-Object System.Windows.Forms.Label
    $lblTotalPaid.Text = "Total Paid Towards Bills: $($totalPaid.ToString('C'))"
    $lblTotalPaid.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Regular)
    $lblTotalPaid.Location = New-Object System.Drawing.Point(20, 80)
    $lblTotalPaid.Size = New-Object System.Drawing.Size(460, 20)
    $summaryForm.Controls.Add($lblTotalPaid)
    
    # Create pie chart panel - moved to top right with extra width for legend
    $pieChartPanel = New-Object System.Windows.Forms.Panel
    $pieChartPanel.Location = New-Object System.Drawing.Point(500, 20)
    $pieChartPanel.Size = New-Object System.Drawing.Size(470, 320)
    $pieChartPanel.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle

    # Create pie chart drawing function
    $pieChartPanel.Add_Paint({
        param($sender, $e)

        $graphics = $e.Graphics
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias

        # Calculate total for percentages
        $grandTotal = ($categoryTotals.Values | Measure-Object -Sum).Sum
        if ($grandTotal -eq 0) { return }

        # Define colors for categories
        $colors = @(
            [System.Drawing.Color]::Red,
            [System.Drawing.Color]::Blue,
            [System.Drawing.Color]::Green,
            [System.Drawing.Color]::Orange,
            [System.Drawing.Color]::Purple,
            [System.Drawing.Color]::Brown,
            [System.Drawing.Color]::Pink,
            [System.Drawing.Color]::Gray,
            [System.Drawing.Color]::Olive,
            [System.Drawing.Color]::Navy,
            [System.Drawing.Color]::Teal,
            [System.Drawing.Color]::Maroon
        )

        # Draw pie chart - larger size
        $rect = New-Object System.Drawing.Rectangle(20, 20, 220, 220)
        $startAngle = 0
        $colorIndex = 0

        # Draw legend - positioned to the right of the larger pie
        $legendY = 20
        $legendX = 260

        foreach ($category in $categoryTotals.Keys) {
            $amount = $categoryTotals[$category]
            if ($amount -gt 0) {
                $percentage = ($amount / $grandTotal) * 100
                $sweepAngle = ($amount / $grandTotal) * 360

                # Draw pie slice
                $brush = New-Object System.Drawing.SolidBrush($colors[$colorIndex % $colors.Length])
                $graphics.FillPie($brush, $rect, $startAngle, $sweepAngle)

                # Draw legend item
                $legendRect = New-Object System.Drawing.Rectangle($legendX, $legendY, 15, 15)
                $graphics.FillRectangle($brush, $legendRect)

                $legendText = "$category - $($amount.ToString('C')) ({0:F1}%)" -f $percentage
                $font = New-Object System.Drawing.Font("Arial", 9)
                $graphics.DrawString($legendText, $font, [System.Drawing.Brushes]::Black, ($legendX + 20), $legendY)

                $startAngle += $sweepAngle
                $legendY += 20
                $colorIndex++

                $brush.Dispose()
                $font.Dispose()
            }
        }
    })

    $summaryForm.Controls.Add($pieChartPanel)

    # Add a label for total amount actually paid
    $lblTotalAmountPaid = New-Object System.Windows.Forms.Label
    $lblTotalAmountPaid.Text = "Total Amount Actually Paid: $($totalAmountPaid.ToString('C'))"
    $lblTotalAmountPaid.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Regular)
    $lblTotalAmountPaid.Location = New-Object System.Drawing.Point(20, 100)
    $lblTotalAmountPaid.Size = New-Object System.Drawing.Size(460, 20)
    $summaryForm.Controls.Add($lblTotalAmountPaid)
    
    # Add a label for overpayment/underpayment
    $lblOverpayment = New-Object System.Windows.Forms.Label
    if ($overpayment -lt 0) {
        $lblOverpayment.Text = "Total Underpayments: $($overpayment.ToString('C'))"
        $lblOverpayment.ForeColor = [System.Drawing.Color]::Orange
    } else {
        $lblOverpayment.Text = "Total Overpayments: $($overpayment.ToString('C'))"
        if ($overpayment -gt 0) {
            $lblOverpayment.ForeColor = [System.Drawing.Color]::Blue
        }
    }
    $lblOverpayment.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
    $lblOverpayment.Location = New-Object System.Drawing.Point(20, 120)
    $lblOverpayment.Size = New-Object System.Drawing.Size(460, 20)
    $summaryForm.Controls.Add($lblOverpayment)
    
    # Add a label for combined total
    $lblTotal = New-Object System.Windows.Forms.Label
    $lblTotal.Text = "Total Monthly Bills: $($($totalDue + $totalPaid).ToString('C'))"
    $lblTotal.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
    $lblTotal.Location = New-Object System.Drawing.Point(20, 140)
    $lblTotal.Size = New-Object System.Drawing.Size(460, 20)
    $summaryForm.Controls.Add($lblTotal)

    # Add category breakdown section - positioned in left column
    $lblCategoryTitle = New-Object System.Windows.Forms.Label
    $lblCategoryTitle.Text = "Category Breakdown:"
    $lblCategoryTitle.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
    $lblCategoryTitle.Location = New-Object System.Drawing.Point(20, 170)
    $lblCategoryTitle.Size = New-Object System.Drawing.Size(200, 20)
    $summaryForm.Controls.Add($lblCategoryTitle)

    # Create category breakdown text
    $categoryBreakdown = ""
    $sortedCategories = $categoryTotals.GetEnumerator() | Where-Object { $_.Value -gt 0 } | Sort-Object Value -Descending
    foreach ($categoryPair in $sortedCategories) {
        $percentage = if ($grandTotal -gt 0) { ($categoryPair.Value / $grandTotal) * 100 } else { 0 }
        $categoryBreakdown += "$($categoryPair.Key): $($categoryPair.Value.ToString('C')) ({0:F1}%)`r`n" -f $percentage
    }

    $lblCategoryBreakdown = New-Object System.Windows.Forms.Label
    $lblCategoryBreakdown.Text = $categoryBreakdown
    $lblCategoryBreakdown.Font = New-Object System.Drawing.Font("Arial", 9, [System.Drawing.FontStyle]::Regular)
    $lblCategoryBreakdown.Location = New-Object System.Drawing.Point(20, 190)
    $lblCategoryBreakdown.Size = New-Object System.Drawing.Size(460, 150)
    $summaryForm.Controls.Add($lblCategoryBreakdown)

    # Add a label for unpaid bills - positioned at bottom left
    $lblUnpaidTitle = New-Object System.Windows.Forms.Label
    $lblUnpaidTitle.Text = "Unpaid Bills:"
    $lblUnpaidTitle.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
    $lblUnpaidTitle.Location = New-Object System.Drawing.Point(20, 360)
    $lblUnpaidTitle.Size = New-Object System.Drawing.Size(285, 20)
    $summaryForm.Controls.Add($lblUnpaidTitle)

    # Add a listbox for unpaid bills - positioned at bottom left
    $lstUnpaid = New-Object System.Windows.Forms.ListBox
    $lstUnpaid.Location = New-Object System.Drawing.Point(20, 380)
    $lstUnpaid.Size = New-Object System.Drawing.Size(460, 220)
    $lstUnpaid.Font = New-Object System.Drawing.Font("Arial", 9)
    foreach ($bill in $unpaidBills) {
        $lstUnpaid.Items.Add($bill)
    }
    $summaryForm.Controls.Add($lstUnpaid)

    # Add a label for paid bills - positioned at bottom right
    $lblPaidTitle = New-Object System.Windows.Forms.Label
    $lblPaidTitle.Text = "Paid Bills:"
    $lblPaidTitle.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
    $lblPaidTitle.Location = New-Object System.Drawing.Point(500, 360)
    $lblPaidTitle.Size = New-Object System.Drawing.Size(285, 20)
    $summaryForm.Controls.Add($lblPaidTitle)

    # Add a listbox for paid bills - positioned at bottom right
    $lstPaid = New-Object System.Windows.Forms.ListBox
    $lstPaid.Location = New-Object System.Drawing.Point(500, 380)
    $lstPaid.Size = New-Object System.Drawing.Size(470, 220)
    $lstPaid.Font = New-Object System.Drawing.Font("Arial", 9)
    foreach ($bill in $paidBills) {
        $lstPaid.Items.Add($bill)
    }
    $summaryForm.Controls.Add($lstPaid)

    # Add a close button - centered at bottom
    $btnClose = New-Object System.Windows.Forms.Button
    $btnClose.Text = "Close"
    $btnClose.DialogResult = [System.Windows.Forms.DialogResult]::OK
    $btnClose.Location = New-Object System.Drawing.Point(450, 620)
    $btnClose.Size = New-Object System.Drawing.Size(100, 30)
    $summaryForm.Controls.Add($btnClose)

    $summaryForm.AcceptButton = $btnClose
    $summaryForm.ShowDialog()
})

# Add the Monthly Summary button to the form
$form.Controls.Add($btnMonthlySummary)

# Add Template buttons
$btnCreateTemplate = New-Object System.Windows.Forms.Button
$btnCreateTemplate.Text = "Create Template"
$btnCreateTemplate.Location = New-Object System.Drawing.Point(20, 65)
$btnCreateTemplate.Size = New-Object System.Drawing.Size(120, 30)
$btnCreateTemplate.BackColor = [System.Drawing.Color]::LightSalmon
$btnCreateTemplate.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat

$btnLoadTemplate = New-Object System.Windows.Forms.Button
$btnLoadTemplate.Text = "Load Template"
$btnLoadTemplate.Location = New-Object System.Drawing.Point(160, 65)
$btnLoadTemplate.Size = New-Object System.Drawing.Size(120, 30)
$btnLoadTemplate.BackColor = [System.Drawing.Color]::LightSalmon
$btnLoadTemplate.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat

# Function to create a template
$btnCreateTemplate.Add_Click({
    $saveFileDialog = New-Object System.Windows.Forms.SaveFileDialog
    $saveFileDialog.Filter = "XML Template files (*.xmlt)|*.xmlt|All files (*.*)|*.*"
    $saveFileDialog.DefaultExt = "xmlt"
    $saveFileDialog.AddExtension = $true
    
    if ($saveFileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        # Create template array
        $templates = @()
        
        foreach ($item in $listView.Items) {
            # Extract the day from the due date
            $dueDay = 1  # Default day if parsing fails
            try {
                $dueDate = [DateTime]::ParseExact($item.SubItems[2].Text, "MM/dd/yyyy", $null)
                $dueDay = $dueDate.Day
            } catch {
                # If parsing fails, use default day
            }
            
            $template = New-Object PSObject -Property @{
                Name = $item.Text
                Amount = $item.SubItems[1].Text
                DueDay = $dueDay
                Status = "Unpaid"
                ConfirmationNumber = ""
                Category = if ($item.SubItems.Count -gt 6) { $item.SubItems[6].Text } else { "Miscellaneous" }
                Notes = if ($item.SubItems.Count -gt 7) { $item.SubItems[7].Text } else { "" }
            }
            
            $templates += $template
        }
        
        # Save template to file
        $templates | Export-Clixml -Path $saveFileDialog.FileName
        
        [System.Windows.Forms.MessageBox]::Show("Template saved successfully!", "Template Created", 
            [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    }
})

# Function to load a template
$btnLoadTemplate.Add_Click({
    $openFileDialog = New-Object System.Windows.Forms.OpenFileDialog
    $openFileDialog.Filter = "XML Template files (*.xmlt)|*.xmlt|All files (*.*)|*.*"
    
    if ($openFileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        try {
            # Load templates from file
            $templates = Import-Clixml -Path $openFileDialog.FileName
            
            # Get current month and year
            $currentDate = Get-Date
            $currentMonth = $currentDate.Month
            $currentYear = $currentDate.Year
            
            # If today is past the 25th, set due dates to next month
            if ($currentDate.Day -gt 25) {
                $nextMonth = $currentDate.AddMonths(1)
                $currentMonth = $nextMonth.Month
                $currentYear = $nextMonth.Year
            }
            
            # Clear existing items
            $listView.Items.Clear()
            
            foreach ($template in $templates) {
                # Create new item with the template name
                $item = New-Object System.Windows.Forms.ListViewItem($template.Name)
                
                # Add amount
                $item.SubItems.Add($template.Amount)
                
                # Create due date using the saved day but current/next month and year
                $dueDay = [Math]::Min($template.DueDay, [DateTime]::DaysInMonth($currentYear, $currentMonth))
                $dueDate = Get-Date -Year $currentYear -Month $currentMonth -Day $dueDay
                
                $item.SubItems.Add($dueDate.ToString("MM/dd/yyyy"))
                $item.SubItems.Add("Unpaid")
                $item.SubItems.Add("")  # Empty amount paid
                $item.SubItems.Add("")  # Empty confirmation number

                # Add category if it exists (for backward compatibility)
                if ($null -ne $template.Category) {
                    $item.SubItems.Add($template.Category)
                } else {
                    $item.SubItems.Add("Miscellaneous")  # Default category for old templates
                }

                # Add notes if they exist
                if ($null -ne $template.Notes) {
                    $item.SubItems.Add($template.Notes)
                } else {
                    $item.SubItems.Add("")
                }
                
                # Update color based on payment status and due date
                Update-ItemColors -item $item
                
                # Add the item to the list view
                $listView.Items.Add($item)
            }
            
            [System.Windows.Forms.MessageBox]::Show("Template loaded successfully!", "Template Loaded", 
                [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
        }
        catch {
            [System.Windows.Forms.MessageBox]::Show("Error loading template: $_", "Load Error", 
                [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
        }
    }
})

# Create a group box for reporting functions --------------------------------------------------------------
$grpReporting = New-Object System.Windows.Forms.GroupBox
# $grpReporting.Text = "Reports and Templates"
$grpReporting.Location = New-Object System.Drawing.Point(35, 685)
$grpReporting.Size = New-Object System.Drawing.Size(880, 53)  # Reduced height from 70 to 65
$grpReporting.Font = New-Object System.Drawing.Font("Arial", 9, [System.Drawing.FontStyle]::Bold)
$form.Controls.Add($grpReporting)

# Evenly space all buttons
$btnExportCSV.Location = New-Object System.Drawing.Point(20, 15)
$btnExportCSV.Size = New-Object System.Drawing.Size(200, 30)
$btnExportCSV.BackColor = [System.Drawing.Color]::LightBlue
$btnExportCSV.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat

$btnMonthlySummary.Location = New-Object System.Drawing.Point(240, 15)
$btnMonthlySummary.Size = New-Object System.Drawing.Size(200, 30)
$btnMonthlySummary.BackColor = [System.Drawing.Color]::LightBlue
$btnMonthlySummary.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat

$btnCreateTemplate.Location = New-Object System.Drawing.Point(460, 15)
$btnCreateTemplate.Size = New-Object System.Drawing.Size(200, 30)

$btnLoadTemplate.Location = New-Object System.Drawing.Point(680, 15)
$btnLoadTemplate.Size = New-Object System.Drawing.Size(180, 30)

# Add the template buttons to the group box
$grpReporting.Controls.Add($btnExportCSV)
$grpReporting.Controls.Add($btnMonthlySummary)
$grpReporting.Controls.Add($btnCreateTemplate)
$grpReporting.Controls.Add($btnLoadTemplate)

# Add a timer to periodically update colors based on due dates
$timer = New-Object System.Windows.Forms.Timer
$timer.Interval = 3600000  # Check once per hour (3,600,000 milliseconds)
$timer.Add_Tick({
    foreach ($item in $listView.Items) {
        Update-ItemColors -item $item
    }
})
$timer.Start()

# Add auto-save functionality
# Get the script's directory for default auto-save location
$scriptDirectory = if ($PSScriptRoot) {
    $PSScriptRoot  # PowerShell v3.0 and later
} elseif ($MyInvocation.MyCommand.Path) {
    Split-Path -Parent -Path $MyInvocation.MyCommand.Path
} else {
    $PWD.Path  # Fallback to current directory
}
$script:autoSavePath = Join-Path -Path $scriptDirectory -ChildPath "BillingManager_AutoSave.xml"
$autoSaveTimer = New-Object System.Windows.Forms.Timer
$autoSaveTimer.Interval = 300000  # Auto-save every 5 minutes (300,000 ms)
$autoSaveTimer.Add_Tick({
    # Only auto-save if there are bills in the list
    if ($listView.Items.Count -gt 0) {
        try {
            # Use the custom auto-save path
            $bills = @()
            foreach ($item in $listView.Items) {
                # Ensure we have all SubItems
                while ($item.SubItems.Count -lt 7) {
                    $item.SubItems.Add("")
                }
                
                # Check if the bill is paid and extract payment date if available
                $isPaid = $item.SubItems[3].Text.StartsWith("Paid")
                $paymentDate = ""
                if ($isPaid -and $item.SubItems[3].Text -match "Paid on (\d{2}/\d{2}/\d{4})") {
                    $paymentDate = $matches[1]
                }
                
                $bill = @{
                    Name = $item.Text
                    Amount = $item.SubItems[1].Text
                    DueDate = $item.SubItems[2].Text
                    Status = $item.SubItems[3].Text
                    AmountPaid = $item.SubItems[4].Text
                    ConfirmationNumber = $item.SubItems[5].Text
                    Notes = $item.SubItems[6].Text
                    PaymentDate = $paymentDate
                    IsPaid = $isPaid
                }
                
                # Add to bills array
                $bills += $bill
            }
            
            # Export to XML
            $bills | Export-Clixml -Path $script:autoSavePath
            
            # Update status bar or show notification (optional)
            # $statusBar.Text = "Auto-saved at $(Get-Date -Format 'HH:mm:ss')"
        } catch {
            # Silent fail for auto-save
        }
    }
})
$autoSaveTimer.Start()

# Add About and Help buttons to the top right - subtle styling with hover effects
$btnAbout = New-Object System.Windows.Forms.Button
$btnAbout.Text = "About"
$btnAbout.Location = New-Object System.Drawing.Point(850, 8)
$btnAbout.Size = New-Object System.Drawing.Size(40, 18)
$btnAbout.Font = New-Object System.Drawing.Font("Arial", 7)
$btnAbout.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
$btnAbout.FlatAppearance.BorderSize = 0  # Remove border
$btnAbout.BackColor = [System.Drawing.Color]::Transparent  # Transparent background
$btnAbout.UseVisualStyleBackColor = $true  # Use visual styles for better hover effect
# Add hover effect
$btnAbout.Add_MouseEnter({
    $this.BackColor = [System.Drawing.Color]::LightGray
})
$btnAbout.Add_MouseLeave({
    $this.BackColor = [System.Drawing.Color]::Transparent
})
$form.Controls.Add($btnAbout)

$btnHelp = New-Object System.Windows.Forms.Button
$btnHelp.Text = "Help"
$btnHelp.Location = New-Object System.Drawing.Point(890, 8)
$btnHelp.Size = New-Object System.Drawing.Size(35, 18)
$btnHelp.Font = New-Object System.Drawing.Font("Arial", 7)
$btnHelp.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
$btnHelp.FlatAppearance.BorderSize = 0  # Remove border
$btnHelp.BackColor = [System.Drawing.Color]::Transparent  # Transparent background
$btnHelp.UseVisualStyleBackColor = $true  # Use visual styles for better hover effect
# Add hover effect
$btnHelp.Add_MouseEnter({
    $this.BackColor = [System.Drawing.Color]::LightGray
})
$btnHelp.Add_MouseLeave({
    $this.BackColor = [System.Drawing.Color]::Transparent
})
$form.Controls.Add($btnHelp)

# Function to show About dialog
$btnAbout.Add_Click({
    $aboutForm = New-Object System.Windows.Forms.Form
    $aboutForm.Text = "About - $scriptName"
    $aboutForm.Size = New-Object System.Drawing.Size(400, 300)
    $aboutForm.StartPosition = "CenterParent"
    $aboutForm.FormBorderStyle = "FixedDialog"
    $aboutForm.MaximizeBox = $false
    $aboutForm.MinimizeBox = $false
    
    # Add logo/icon
    $pictureBox = New-Object System.Windows.Forms.PictureBox
    $pictureBox.Location = New-Object System.Drawing.Point(150, 20)
    $pictureBox.Size = New-Object System.Drawing.Size(100, 100)
    $pictureBox.SizeMode = [System.Windows.Forms.PictureBoxSizeMode]::Zoom
    try {
        $pictureBox.Image = [System.Drawing.Icon]::ExtractAssociatedIcon("PanAura.ico").ToBitmap()
    } catch {
        # If icon loading fails, continue without image
    }
    $aboutForm.Controls.Add($pictureBox)
    
    # Add title
    $lblTitle = New-Object System.Windows.Forms.Label
    $lblTitle.Text = "$scriptName"
    $lblTitle.Font = New-Object System.Drawing.Font("Arial", 14, [System.Drawing.FontStyle]::Bold)
    $lblTitle.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
    $lblTitle.Location = New-Object System.Drawing.Point(20, 130)
    $lblTitle.Size = New-Object System.Drawing.Size(360, 30)
    $aboutForm.Controls.Add($lblTitle)
    
    # Add version
    $lblVersion = New-Object System.Windows.Forms.Label
    $lblVersion.Text = "Version $scriptVersion"
    $lblVersion.Font = New-Object System.Drawing.Font("Arial", 10)
    $lblVersion.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
    $lblVersion.Location = New-Object System.Drawing.Point(20, 160)
    $lblVersion.Size = New-Object System.Drawing.Size(360, 20)
    $aboutForm.Controls.Add($lblVersion)
    
    # Add copyright
    $lblCopyright = New-Object System.Windows.Forms.Label
    $lblCopyright.Text = "Copyright © 2025, Adam Frangione"
    $lblCopyright.Font = New-Object System.Drawing.Font("Arial", 9)
    $lblCopyright.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
    $lblCopyright.Location = New-Object System.Drawing.Point(20, 190)
    $lblCopyright.Size = New-Object System.Drawing.Size(360, 20)
    $aboutForm.Controls.Add($lblCopyright)
    
    # Add close button
    $btnClose = New-Object System.Windows.Forms.Button
    $btnClose.Text = "Close"
    $btnClose.DialogResult = [System.Windows.Forms.DialogResult]::OK
    $btnClose.Location = New-Object System.Drawing.Point(150, 230)
    $btnClose.Size = New-Object System.Drawing.Size(100, 30)
    $aboutForm.Controls.Add($btnClose)
    
    $aboutForm.AcceptButton = $btnClose
    $aboutForm.ShowDialog()
})

# Function to show Help dialog
$btnHelp.Add_Click({
    $helpForm = New-Object System.Windows.Forms.Form
    $helpForm.Text = "$scriptName - Help"
    $helpForm.Size = New-Object System.Drawing.Size(600, 500)
    $helpForm.StartPosition = "CenterParent"
    $helpForm.FormBorderStyle = "Sizable"
    $helpForm.MinimizeBox = $false
    
    # Create a tab control for different help sections
    $tabControl = New-Object System.Windows.Forms.TabControl
    $tabControl.Location = New-Object System.Drawing.Point(10, 10)
    $tabControl.Size = New-Object System.Drawing.Size(565, 400)
    $helpForm.Controls.Add($tabControl)
    
    # Overview tab
    $tabOverview = New-Object System.Windows.Forms.TabPage
    $tabOverview.Text = "Overview"
    $tabControl.Controls.Add($tabOverview)
    
    $txtOverview = New-Object System.Windows.Forms.RichTextBox
    $txtOverview.Location = New-Object System.Drawing.Point(10, 10)
    $txtOverview.Size = New-Object System.Drawing.Size(540, 350)
    $txtOverview.ReadOnly = $true
    $txtOverview.BackColor = [System.Drawing.SystemColors]::Window
    $txtOverview.Font = New-Object System.Drawing.Font("Arial", 9)
    $txtOverview.Text = @"
$($scriptName.ToUpper()) - OVERVIEW

This application helps you track and manage your monthly bills and payments. With $scriptName, you can:

• Add, edit, and delete bills
• Track payment status and due dates
• Mark bills as paid with confirmation numbers
• Save and load your bill data
• Create templates for recurring bills
• Generate monthly summaries
• Export your data to CSV format

The main list shows all your bills with color coding:
• Green: Paid bills
• Red: Unpaid bills that are past due
• Black: Unpaid bills that are not yet due
• Orange: Underpaid bills
• Blue: Overpaid bills

The application automatically sorts bills when you click on column headers and updates colors based on payment status and due dates.

KEYBOARD SHORTCUTS:
• Ctrl+A: Add a new bill
• Ctrl+E: Edit selected bill
• Ctrl+P: Mark selected bill(s) as paid
• Delete: Delete selected bill(s)
• Ctrl+S: Save bills (if implemented)
• Ctrl+O: Open/load bills (if implemented)

"@
    $tabOverview.Controls.Add($txtOverview)
    
    # Adding Bills tab
    $tabAddingBills = New-Object System.Windows.Forms.TabPage
    $tabAddingBills.Text = "Adding Bills"
    $tabControl.Controls.Add($tabAddingBills)
    
    $txtAddingBills = New-Object System.Windows.Forms.RichTextBox
    $txtAddingBills.Location = New-Object System.Drawing.Point(10, 10)
    $txtAddingBills.Size = New-Object System.Drawing.Size(540, 350)
    $txtAddingBills.ReadOnly = $true
    $txtAddingBills.BackColor = [System.Drawing.SystemColors]::Window
    $txtAddingBills.Font = New-Object System.Drawing.Font("Arial", 9)
    $txtAddingBills.Text = @"
ADDING AND MANAGING BILLS

To add a new bill:
1. Enter the bill name in the "Bill Name" field
2. Enter the amount in the "Amount" field
3. Select the due date using the date picker
4. Optionally enter a confirmation number
5. Click the "Add Bill" button

To edit a bill:
1. Select the bill in the list
2. Click the "Edit Bill" button
3. Modify the details in the dialog that appears
4. Click "Save" to apply your changes

To delete a bill:
1. Select one or more bills in the list
2. Click the "Delete Bill" button
3. Confirm the deletion when prompted

To mark a bill as paid:
1. Select one or more unpaid bills
2. Click the "Mark as Paid" button
3. Enter the amount paid and confirmation number
4. Click "OK" to update the payment status

To unmark a bill as paid:
1. Select one or more paid bills
2. Click the "Mark as paid" button again
3. Bills will now be set back to unpaid status

"@
    $tabAddingBills.Controls.Add($txtAddingBills)
    
    # Templates tab
    $tabTemplates = New-Object System.Windows.Forms.TabPage
    $tabTemplates.Text = "Templates"
    $tabControl.Controls.Add($tabTemplates)
    
    $txtTemplates = New-Object System.Windows.Forms.RichTextBox
    $txtTemplates.Location = New-Object System.Drawing.Point(10, 10)
    $txtTemplates.Size = New-Object System.Drawing.Size(540, 350)
    $txtTemplates.ReadOnly = $true
    $txtTemplates.BackColor = [System.Drawing.SystemColors]::Window
    $txtTemplates.Font = New-Object System.Drawing.Font("Arial", 9)
    $txtTemplates.Text = @"
USING TEMPLATES

Templates allow you to save your recurring bills and quickly load them at the beginning of each month.

To create a template:
1. Add all your recurring bills to the list
2. Click the "Create Template" button
3. Choose a location to save the template file (.xmlt)
4. Click "Save"

To load a template:
1. Click the "Load Template" button
2. Browse to your saved template file
3. Click "Open"

When loading a template:
• All bills will be added with their original names and amounts
• Due dates will be updated to the current month while preserving the day
• If today is past the 25th of the month, due dates will be set to the next month
• All bills will be marked as "Unpaid" with empty payment information

Templates are perfect for monthly recurring bills like rent, utilities, subscriptions, etc.

"@
    $tabTemplates.Controls.Add($txtTemplates)
    
    # Reports tab
    $tabReports = New-Object System.Windows.Forms.TabPage
    $tabReports.Text = "Reports"
    $tabControl.Controls.Add($tabReports)
    
    $txtReports = New-Object System.Windows.Forms.RichTextBox
    $txtReports.Location = New-Object System.Drawing.Point(10, 10)
    $txtReports.Size = New-Object System.Drawing.Size(540, 350)
    $txtReports.ReadOnly = $true
    $txtReports.BackColor = [System.Drawing.SystemColors]::Window
    $txtReports.Font = New-Object System.Drawing.Font("Arial", 9)
    $txtReports.Text = @"
REPORTS AND EXPORTS

Monthly Summary:
1. Click the "Monthly Summary" button
2. A new window will show:
   • Total amount due for the current month
   • Total amount paid for the current month
   • Lists of paid and unpaid bills
   • Percentage of bills paid

Export to CSV:
1. Click the "Export to CSV" button
2. Choose a location to save the CSV file
3. Click "Save"

The CSV file can be opened in Excel or other spreadsheet applications for further analysis or record-keeping.

Saving and Loading:
• Use "Save Bills" to save your current bill list to an XML file
• Use "Load Bills" to load a previously saved bill list
• The current file name is displayed at the top of the application

"@
    $tabReports.Controls.Add($txtReports)
    
    # Add a new Shortcuts tab
    $tabShortcuts = New-Object System.Windows.Forms.TabPage
    $tabShortcuts.Text = "Shortcuts"
    $tabControl.Controls.Add($tabShortcuts)
    
    $txtShortcuts = New-Object System.Windows.Forms.RichTextBox
    $txtShortcuts.Location = New-Object System.Drawing.Point(10, 10)
    $txtShortcuts.Size = New-Object System.Drawing.Size(540, 350)
    $txtShortcuts.ReadOnly = $true
    $txtShortcuts.BackColor = [System.Drawing.SystemColors]::Window
    $txtShortcuts.Font = New-Object System.Drawing.Font("Arial", 9)
    $txtShortcuts.Text = @"
KEYBOARD SHORTCUTS

$scriptName includes several keyboard shortcuts to help you work more efficiently:

Bill Management:
• Ctrl+A: Add a new bill
• Ctrl+B: Focus the Bill Name field
• Ctrl+E: Edit the selected bill
• Ctrl+P: Mark selected bill(s) as paid
• Delete: Delete selected bill(s)

Navigation:
• Tab: Move between input fields
• Enter: Activate the focused button
• Arrow keys: Navigate within the bill list

Search:
• Ctrl+F: Focus the search box
• Escape: Clear search and show all bills (if implemented)

File Operations:
• Ctrl+S: Save bills
• Ctrl+O: Open/load bills
• Ctrl+N: New/clear bill list (if implemented)

Other:
• F1: Show this help dialog
• Alt+F4: Exit the application
"@
    $tabShortcuts.Controls.Add($txtShortcuts)
    
    # Add a new Auto-Save tab
    $tabAutoSave = New-Object System.Windows.Forms.TabPage
    $tabAutoSave.Text = "Auto-Save"
    $tabControl.Controls.Add($tabAutoSave)
    
    $txtAutoSave = New-Object System.Windows.Forms.RichTextBox
    $txtAutoSave.Location = New-Object System.Drawing.Point(10, 10)
    $txtAutoSave.Size = New-Object System.Drawing.Size(540, 230) # Reduced height to make room for controls
    $txtAutoSave.ReadOnly = $true
    $txtAutoSave.BackColor = [System.Drawing.SystemColors]::Window
    $txtAutoSave.Font = New-Object System.Drawing.Font("Arial", 9)
    $txtAutoSave.Text = @"
AUTO-SAVE FUNCTIONALITY

$scriptName includes an automatic saving feature to help prevent data loss. Here's how it works:

How Auto-Save Works:
• The application automatically saves your bill data every 5 minutes
• Auto-save only occurs when you have at least one bill in your list
• Your data is saved to the location specified below
• This happens silently in the background without interrupting your work

What Gets Saved:
• All bill information including names, amounts, due dates, and payment status
• Payment details such as amount paid and confirmation numbers
• Notes and other custom information you've added

Recovery:
• If the application closes unexpectedly, your data is preserved in the auto-save file
• When you restart the application, you can load this file using the "Load Bills" button

Manual Saving:
• Auto-save is not a replacement for manually saving your data
• It's still recommended to use the "Save Bills" button (or Ctrl+S) to save your work
• Manual saves allow you to choose the file name and location

"@
    $tabAutoSave.Controls.Add($txtAutoSave)
    
    # Add message about custom save location
    $lblCustomSaveNote = New-Object System.Windows.Forms.Label
    $lblCustomSaveNote.Text = "Note: Custom save location is used only during current session and will not be remembered."
    $lblCustomSaveNote.Location = New-Object System.Drawing.Point(10, 265)
    $lblCustomSaveNote.Size = New-Object System.Drawing.Size(540, 20)
    $lblCustomSaveNote.Font = New-Object System.Drawing.Font("Arial", 8, [System.Drawing.FontStyle]::Italic)
    $lblCustomSaveNote.ForeColor = [System.Drawing.Color]::DarkBlue
    $tabAutoSave.Controls.Add($lblCustomSaveNote)
    
    # Add label for auto-save location
    $lblAutoSavePath = New-Object System.Windows.Forms.Label
    $lblAutoSavePath.Text = "Auto-Save Location:"
    $lblAutoSavePath.Location = New-Object System.Drawing.Point(10, 290)
    $lblAutoSavePath.Size = New-Object System.Drawing.Size(120, 20)
    $tabAutoSave.Controls.Add($lblAutoSavePath)
    
    # Add textbox to display/edit the path
    $txtAutoSavePath = New-Object System.Windows.Forms.TextBox
    $txtAutoSavePath.Location = New-Object System.Drawing.Point(130, 290)
    $txtAutoSavePath.Size = New-Object System.Drawing.Size(300, 20)
    $txtAutoSavePath.Text = $script:autoSavePath
    $txtAutoSavePath.ReadOnly = $true # Make it read-only initially
    $tabAutoSave.Controls.Add($txtAutoSavePath)
    
    # Add browse button
    $btnBrowseAutoSave = New-Object System.Windows.Forms.Button
    $btnBrowseAutoSave.Text = "Browse..."
    $btnBrowseAutoSave.Location = New-Object System.Drawing.Point(440, 288)
    $btnBrowseAutoSave.Size = New-Object System.Drawing.Size(80, 23)
    $tabAutoSave.Controls.Add($btnBrowseAutoSave)
    
    # Add save button for the path
    $btnSaveAutoSavePath = New-Object System.Windows.Forms.Button
    $btnSaveAutoSavePath.Text = "Set as Default"
    $btnSaveAutoSavePath.Location = New-Object System.Drawing.Point(350, 320)
    $btnSaveAutoSavePath.Size = New-Object System.Drawing.Size(170, 30)
    $tabAutoSave.Controls.Add($btnSaveAutoSavePath)
    
    # Browse button click event
    $btnBrowseAutoSave.Add_Click({
        $saveFileDialog = New-Object System.Windows.Forms.SaveFileDialog
        $saveFileDialog.Filter = "XML files (*.xml)|*.xml|All files (*.*)|*.*"
        $saveFileDialog.DefaultExt = "xml"
        $saveFileDialog.AddExtension = $true
        $saveFileDialog.FileName = "BillingManager_AutoSave.xml"
        $saveFileDialog.Title = "Select Auto-Save Location"
        
        if ($saveFileDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
            $txtAutoSavePath.Text = $saveFileDialog.FileName
        }
    })
    
    # Save path button click event
    $btnSaveAutoSavePath.Add_Click({
        $script:autoSavePath = $txtAutoSavePath.Text
        [System.Windows.Forms.MessageBox]::Show(
            "Auto-save location has been updated to:`n$script:autoSavePath", 
            "Auto-Save Location Updated", 
            [System.Windows.Forms.MessageBoxButtons]::OK, 
            [System.Windows.Forms.MessageBoxIcon]::Information)
    })
    
    # Add close button
    $btnClose = New-Object System.Windows.Forms.Button
    $btnClose.Text = "Close"
    $btnClose.DialogResult = [System.Windows.Forms.DialogResult]::OK
    $btnClose.Location = New-Object System.Drawing.Point(250, 420)
    $btnClose.Size = New-Object System.Drawing.Size(100, 30)
    $helpForm.Controls.Add($btnClose)

    $helpForm.AcceptButton = $btnClose
    $helpForm.ShowDialog()
})

# Adjust the current file label to make room for search box
$lblCurrentFile.Size = New-Object System.Drawing.Size(660, 25)  # Reduced width from 880 to 660

# Add search box
$lblSearch = New-Object System.Windows.Forms.Label
$lblSearch.Text = "Search:"
$lblSearch.Location = New-Object System.Drawing.Point(710, 38) # Top right corner
$lblSearch.Size = New-Object System.Drawing.Size(50, 24)
$form.Controls.Add($lblSearch)

$txtSearch = New-Object System.Windows.Forms.TextBox
$txtSearch.Location = New-Object System.Drawing.Point(765, 35) # Top right corner
$txtSearch.Size = New-Object System.Drawing.Size(166, 20)
$form.Controls.Add($txtSearch)

# Add search functionality without trying to remove previous handlers
$txtSearch.Add_TextChanged({
    $searchText = $txtSearch.Text.ToLower()
    
    if ([string]::IsNullOrWhiteSpace($searchText)) {
        # If search is empty, show all items
        foreach ($item in $listView.Items) {
            $item.BackColor = [System.Drawing.SystemColors]::Window
        }
    } else {
        # Otherwise, highlight matching items
        foreach ($item in $listView.Items) {
            $found = $false
            
            # Search in all columns
            for ($i = 0; $i -lt $item.SubItems.Count; $i++) {
                if ($item.SubItems[$i].Text.ToLower().Contains($searchText)) {
                    $found = $true
                    break
                }
            }
            
            # Highlight matching items
            if ($found) {
                $item.BackColor = [System.Drawing.Color]::LightYellow
            } else {
                $item.BackColor = [System.Drawing.SystemColors]::Window
            }
        }
    }
})

# Add status bar with adjusted position
$statusBar = New-Object System.Windows.Forms.StatusBar
$statusBar.Text = "Ready"
$statusBar.Top = $form.ClientSize.Height - $statusBar.Height - 5  # Position at bottom with 5px margin
$form.Controls.Add($statusBar)

# Update status bar when actions are performed
$btnAdd.Add_Click({
    # Existing code remains unchanged
    $statusBar.Text = "Bill added: $($txtName.Text)"
})

$btnDelete.Add_Click({
    # Existing code remains unchanged
    $statusBar.Text = "Deleted $($selectedItems.Count) bill(s)"
})

$btnMarkPaid.Add_Click({
    # Existing code remains unchanged
    $statusBar.Text = "Updated payment status for $($selectedItems.Count) bill(s)"
})

# Add keyboard shortcuts
$form.KeyPreview = $true
$form.Add_KeyDown({
    param($keyboardSender, $e)
    
    # Ctrl+A to add a bill
    if ($e.Control -and $e.KeyCode -eq 'A') {
        $btnAdd.PerformClick()
    }
    # Ctrl+E to edit selected bill
    elseif ($e.Control -and $e.KeyCode -eq 'E') {
        $btnEdit.PerformClick()
    }
    # Ctrl+P to mark as paid
    elseif ($e.Control -and $e.KeyCode -eq 'P') {
        $btnMarkPaid.PerformClick()
    }
    # Ctrl+S to save bills
    elseif ($e.Control -and $e.KeyCode -eq 'S') {
        $btnSave.PerformClick()
    }
    # Ctrl+O to load bills
    elseif ($e.Control -and $e.KeyCode -eq 'O') {
        $btnLoad.PerformClick()
    }
    # Ctrl+F to focus search box
    elseif ($e.Control -and $e.KeyCode -eq 'F') {
        $txtSearch.Focus()
        $txtSearch.SelectAll()  # Select all text for easy replacement
    }
    # Ctrl+B to focus the Bill Name field
    elseif ($e.Control -and $e.KeyCode -eq 'B') {
        $txtName.Focus()
        $txtName.SelectAll()  # Select all text for easy replacement
    }
    # Delete key to delete selected bills
    elseif ($e.KeyCode -eq 'Delete') {
        $btnDelete.PerformClick()
    }
    # F1 key to open help
    elseif ($e.KeyCode -eq 'F1') {
        $btnHelp.PerformClick()
    }
})

# Add this function near the end of your script, before $form.ShowDialog()
function Get-UpcomingBills {
    $upcomingBills = @()
    $today = Get-Date
    $warningDays = 5 # Days before due date to show warning
    
    foreach ($item in $listView.Items) {
        if ($item.SubItems[3].Text -eq "Unpaid") {
            try {
                $dueDate = [DateTime]::ParseExact($item.SubItems[2].Text, "MM/dd/yyyy", $null)
                $daysUntilDue = ($dueDate - $today).Days
                
                if ($daysUntilDue -ge 0 -and $daysUntilDue -le $warningDays) {
                    $upcomingBills += @{
                        Name = $item.Text
                        Amount = $item.SubItems[1].Text
                        DueDate = $item.SubItems[2].Text
                        DaysUntilDue = $daysUntilDue
                    }
                }
            } catch {
                # Skip if date parsing fails
            }
        }
    }
    
    return $upcomingBills
}

# Add this function to check and display upcoming bills
function Show-UpcomingBillsReminder {
    $upcomingBills = Get-UpcomingBills
    
    if ($upcomingBills.Count -gt 0) {
        $message = "You have upcoming bills due soon:`n`n"
        foreach ($bill in $upcomingBills) {
            $message += "$($bill.Name): " + "$" + "$($bill.Amount) due in $($bill.DaysUntilDue) day"
            if ($bill.DaysUntilDue -ne 1) { $message += "s" }
            $message += " ($($bill.DueDate))`n"
        }
        
        [System.Windows.Forms.MessageBox]::Show(
            $message,
            "Upcoming Bills Reminder",
            [System.Windows.Forms.MessageBoxButtons]::OK,
            [System.Windows.Forms.MessageBoxIcon]::Information)
    }
}

# Find the existing $btnLoad.Add_Click event handler in your script
# and add a call to Show-UpcomingBillsReminder right after the "Bills loaded successfully!" message
# It should look something like this:

# Find this line in your existing code:
# [System.Windows.Forms.MessageBox]::Show("Bills loaded successfully!", "Load Complete", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)

# And add this line right after it:
# Show-UpcomingBillsReminder

# Also add the form's Shown event handler to check on startup
$form.Add_Shown({
    Show-UpcomingBillsReminder
})

# Show the form
$form.ShowDialog()







# SIG # Begin signature block
# MIIFagYJKoZIhvcNAQcCoIIFWzCCBVcCAQExCzAJBgUrDgMCGgUAMGkGCisGAQQB
# gjcCAQSgWzBZMDQGCisGAQQBgjcCAR4wJgIDAQAABBAfzDtgWUsITrck0sYpfvNR
# AgEAAgEAAgEAAgEAAgEAMCEwCQYFKw4DAhoFAAQUHpM5vPMWrclABdun8l6eN84v
# I4ygggMGMIIDAjCCAeqgAwIBAgIQGsLyt53IMqpJfP+GTtXWAjANBgkqhkiG9w0B
# AQsFADAZMRcwFQYDVQQDDA5BZGFtIEZyYW5naW9uZTAeFw0yNTA2MDMwNjAwMjJa
# Fw0yODA2MDMwNjEwMjFaMBkxFzAVBgNVBAMMDkFkYW0gRnJhbmdpb25lMIIBIjAN
# BgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwwM8cIMWxT/TjM6nMlr4bTd17Xw7
# qgkeEFqjXsD0nVJLloMu70LGS2ul4yIR0VSgyZC7qw0A3b5GrNUsasCmz91O/Sb4
# D1mjVXxJaOG99U5gx8pltYyk7QOQ6SFc0afs9Cw3CWgpjWL79BbqFzNCTcExNRMD
# uqi6Dt6SGc+yt+TKwGTPb4X1t2kni3f8SEaaPaXOMZbgNXDTL1cd9d/D1HhjnktJ
# 0ZAeHOeVc8EKBJpHnrcfBp+8/cPRMbBuI50APEN0o6UpwX0FJFmmoo1SlRj+KPzs
# WsAWkeArTFs38F7Z4MBwkllO5mOAOo1XxYDRBkASdfBlt+JzxaW+oqnSGQIDAQAB
# o0YwRDAOBgNVHQ8BAf8EBAMCB4AwEwYDVR0lBAwwCgYIKwYBBQUHAwMwHQYDVR0O
# BBYEFOOXihi6/0D6mSo2Hq3hzilkKpBxMA0GCSqGSIb3DQEBCwUAA4IBAQBda611
# PAWxiU0yHBnbwb5py4mgSYMWtz+YpzYy+UcMfP38Z03EeMDLNQPFPxsaycjVK8zW
# KIblN7OYOVM7GVkJpfRteQmaVdBmiUOZctE/1FFF4mm2mLDXVqakhib/43a016Yd
# rJJuJM/NVdWuwkhLFQ9+fKsP65kHAxZn2wA4+D4/nPZvR22ccu1r0nuK3w0KgvKe
# 5NUuaNRYSN++KbbY6/3/JzKUDSHqUtpKpSVyuHen4J/eYua7UAdDbxVrKc/2NZua
# m1fi0TP+ML1nLPspEYptg7S+sagTAKFA4mZG6WwsfYy2iRaF948wNuKfX1KCR4ae
# Sqvz0kTaG9PEeWLVMYIBzjCCAcoCAQEwLTAZMRcwFQYDVQQDDA5BZGFtIEZyYW5n
# aW9uZQIQGsLyt53IMqpJfP+GTtXWAjAJBgUrDgMCGgUAoHgwGAYKKwYBBAGCNwIB
# DDEKMAigAoAAoQKAADAZBgkqhkiG9w0BCQMxDAYKKwYBBAGCNwIBBDAcBgorBgEE
# AYI3AgELMQ4wDAYKKwYBBAGCNwIBFTAjBgkqhkiG9w0BCQQxFgQUVYwK7W9A7b+p
# wzBDkS3ISmHty5cwDQYJKoZIhvcNAQEBBQAEggEAr7F+IYH7wvATbA2H14AkgNyu
# 9Gz53wTOpBcBr2tWOYj5tL0seHa+oOEW0eqRY+psA5yYfMzDyahePp/FJ1orKWCZ
# CrZ8/jX5kdp9jrKjBSCjkBImZH4ZVpe0xe/MxcsR7Zvloht6Sxkj2tVy3v5BqZvl
# gfDaaihjC/IRfkqZ0GNxqg8ycFyDvyHWezg+5TTMg4DeBAc0etiPkH6T4YY5znVx
# IN9kFQDCFr45kF7endx5lJXdSYIBoeYi/c9n/ZedWOdlqMA/EbWky8wycRLtircI
# 40qVhPEyDJNHWcpWFLyf5kyZg5H6XeI9TG4w6UzSVRxLv7umlgpxq8HsCAtWqw==
# SIG # End signature block
