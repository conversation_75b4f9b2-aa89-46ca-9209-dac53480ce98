Originally created on the EDIFY-2022 PC using Powershell 7.5.1


	1. ps2exe was already installed by using the following code:
	------>  Install-Module -Name ps2exe -Scope CurrentUser


	2. Then I ran Powershell from the directory that contained my ps1 script (originally named OrganizeFilesByDate.ps1) that I wanted to convert to exe and ran the following code:
	------> ps2exe '.\MyBillingScript.ps1' -noConsole -noOutput -title "My Monthly Bills" -product "My Monthly Billing" -copyright "Copyright (c) 2025, Adam Frangione" -iconFile "PanAura.ico" -version "4.7.1"


	3. Additional things can be done like applying icon to the exe file during conversion using the ps2exe and so to learn more you can using the following code for help:
	------> Get-Help Invoke-ps2exe -Full
	   or simply type:
	------> ps2exe -?


Note:

Be sure to place the icon in the same location as the ps1 file when converting it to the exe (this will prevent it from being blocked by antivirus software)



**** Create the Digital Certificate for the ps1 and exe

1. Use the "CreateCertificate.ps1" (add the password to the script first before running)
2. Use the "SignPS1.ps1" to sign the ps1 script file.
3. Use the "VerifySignature.ps1" to verify that the ps1 was signed.

4. Use the "SignWithSignTool.ps1" if you want to sign the exe using the signtool.exe from windows SDK kit.
   If the signtool.exe is not found, then the version might have been updated and you can search for the exe by using the "FindSignTool.ps1"
   Currently it was installed at "C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\signtool.exe"

5. Instead of using signtool.exe you can use the "SignWithPowerShell.ps1" if you want to use PowerShell to sign the exe (This is what I tend to use because 
   a timestamp server is applied, which ensures the signature remains valid even after the certificate expires.
6. Verify the exe digital Signature certificate via the context menu properties.

*** Keep in mind that each time I use the ps2exe the digital signature will be removed and have to be re-applied.
*** If you want to share the exe file to another computer you will need to share the .pfx file so that the certificate can be installed on the other computer (just double click on the .pfx to install)
    A .pfx includes both the public and private key for the associated certificate (NEVER share this outside your organization) generally used with exe
    A .cer file only has the public key (this is what you typically exchange with integration partners) generally used with network or browsers