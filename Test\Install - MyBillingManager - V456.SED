[Version]
Class=IEXPRESS
SEDVersion=3
[Options]
PackagePurpose=ExtractOnly
ShowInstallProgramWindow=0
HideExtractAnimation=0
UseLongFileName=1
InsideCompressed=0
CAB_FixedSize=0
CAB_ResvCodeSigning=0
RebootMode=I
InstallPrompt=%InstallPrompt%
DisplayLicense=%DisplayLicense%
FinishMessage=%FinishMessage%
TargetName=%TargetName%
FriendlyName=%FriendlyName%
AppLaunched=%AppLaunched%
PostInstallCmd=%PostInstallCmd%
AdminQuietInstCmd=%AdminQuietInstCmd%
UserQuietInstCmd=%UserQuietInstCmd%
SourceFiles=SourceFiles
[Strings]
InstallPrompt=This will install Adam's custom billing program to your computer. Would you like to continue?
DisplayLicense=C:\Users\<USER>\Documents\MY CODE SCRIPTS\Billing Script\My License Agreement - EULA.txt
FinishMessage=MyBillingManager by Adam Frangion has been successfully installed on your computer. Enjoy!.
TargetName=C:\Users\<USER>\Documents\MY CODE SCRIPTS\Billing Script\Test\Install - MyBillingManager - V456.EXE
FriendlyName=Install MyBillingManager
AppLaunched=
PostInstallCmd=
AdminQuietInstCmd=
UserQuietInstCmd=
FILE0="MyBillingScript.exe"
FILE1="MyCodeSigningCert.cer"
FILE2="MyCodeSigningCert.pfx"
FILE3="PanAura.ico"
[SourceFiles]
SourceFiles0=C:\Users\<USER>\Documents\MY CODE SCRIPTS\Billing Script\
[SourceFiles0]
%FILE0%=
%FILE1%=
%FILE2%=
%FILE3%=
