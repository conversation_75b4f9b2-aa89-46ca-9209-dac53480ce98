Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Create the main form
$form = New-Object System.Windows.Forms.Form
$form.Text = "Adam's Billing Manager - v1.0.2"
$form.Size = New-Object System.Drawing.Size(800, 600)
$form.StartPosition = "CenterScreen"
$form.Icon = "PanAura.ico"

# Create a ListView to display billing records
$listView = New-Object System.Windows.Forms.ListView
$listView.View = [System.Windows.Forms.View]::Details
$listView.FullRowSelect = $true
$listView.GridLines = $true
$listView.Location = New-Object System.Drawing.Point(20, 60)
$listView.Size = New-Object System.Drawing.Size(740, 400)

# Add columns to the ListView
$listView.Columns.Add("Bill Name", 150)
$listView.Columns.Add("Amount", 100)
$listView.Columns.Add("Due Date", 100)
$listView.Columns.Add("Status", 100)
$listView.Columns.Add("Notes", 270)

# Add controls for adding new bills
$lblName = New-Object System.Windows.Forms.Label
$lblName.Text = "Bill Name:"
$lblName.Location = New-Object System.Drawing.Point(20, 480)
$lblName.Size = New-Object System.Drawing.Size(80, 20)

$txtName = New-Object System.Windows.Forms.TextBox
$txtName.Location = New-Object System.Drawing.Point(100, 480)
$txtName.Size = New-Object System.Drawing.Size(150, 20)

$lblAmount = New-Object System.Windows.Forms.Label
$lblAmount.Text = "Amount:"
$lblAmount.Location = New-Object System.Drawing.Point(270, 480)
$lblAmount.Size = New-Object System.Drawing.Size(60, 20)

$txtAmount = New-Object System.Windows.Forms.TextBox
$txtAmount.Location = New-Object System.Drawing.Point(330, 480)
$txtAmount.Size = New-Object System.Drawing.Size(80, 20)

$lblDueDate = New-Object System.Windows.Forms.Label
$lblDueDate.Text = "Due Date:"
$lblDueDate.Location = New-Object System.Drawing.Point(430, 480)
$lblDueDate.Size = New-Object System.Drawing.Size(70, 20)

$dtpDueDate = New-Object System.Windows.Forms.DateTimePicker
$dtpDueDate.Location = New-Object System.Drawing.Point(500, 480)
$dtpDueDate.Size = New-Object System.Drawing.Size(120, 20)
$dtpDueDate.Format = [System.Windows.Forms.DateTimePickerFormat]::Short

$btnAdd = New-Object System.Windows.Forms.Button
$btnAdd.Text = "Add Bill"
$btnAdd.Location = New-Object System.Drawing.Point(640, 480)
$btnAdd.Size = New-Object System.Drawing.Size(120, 30)

# Function to add a new bill
$btnAdd.Add_Click({
    if ($txtName.Text -and $txtAmount.Text) {
        $item = New-Object System.Windows.Forms.ListViewItem($txtName.Text)
        $item.SubItems.Add($txtAmount.Text)
        $item.SubItems.Add($dtpDueDate.Value.ToString("MM/dd/yyyy"))
        $item.SubItems.Add("Unpaid")
        $item.SubItems.Add("")
        $listView.Items.Add($item)
        
        # Clear input fields
        $txtName.Text = ""
        $txtAmount.Text = ""
    }
})

# Add controls to the form
$form.Controls.Add($listView)
$form.Controls.Add($lblName)
$form.Controls.Add($txtName)
$form.Controls.Add($lblAmount)
$form.Controls.Add($txtAmount)
$form.Controls.Add($lblDueDate)
$form.Controls.Add($dtpDueDate)
$form.Controls.Add($btnAdd)

# Add a button to mark bills as paid
$btnMarkPaid = New-Object System.Windows.Forms.Button
$btnMarkPaid.Text = "Mark as Paid"
$btnMarkPaid.Location = New-Object System.Drawing.Point(640, 520)
$btnMarkPaid.Size = New-Object System.Drawing.Size(120, 30)

# Function to toggle paid status of selected bills
$btnMarkPaid.Add_Click({
    $selectedItems = $listView.SelectedItems
    if ($selectedItems.Count -gt 0) {
        foreach ($item in $selectedItems) {
            if ($item.SubItems[3].Text -eq "Unpaid") {
                $item.SubItems[3].Text = "Paid"
                $item.ForeColor = [System.Drawing.Color]::Green
            } else {
                $item.SubItems[3].Text = "Unpaid"
                $item.ForeColor = [System.Drawing.Color]::Black
            }
        }
    } else {
        [System.Windows.Forms.MessageBox]::Show("Please select at least one bill to mark as paid.", "No Selection", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    }
})

# Add the button to the form
$form.Controls.Add($btnMarkPaid)

# Show the form
$form.ShowDialog()

